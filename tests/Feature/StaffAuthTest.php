<?php

namespace Tests\Feature;

use App\Models\Staff;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class StaffAuthTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test staff member
        Staff::factory()->create([
            'username' => 'teststaff',
            'password' => Hash::make('password123')
        ]);
    }

    #[Test]
    public function staff_can_login_with_valid_credentials()
    {
        $response = $this->postJson('/api/staff/auth/login', [
            'username' => 'teststaff',
            'password' => 'password123'
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'staff' => [
                            'id',
                            'username',
                            'created_at'
                        ],
                        'token'
                    ]
                ])
                ->assertJson([
                    'success' => true,
                    'message' => 'Login successful',
                    'data' => [
                        'staff' => [
                            'username' => 'teststaff'
                        ]
                    ]
                ]);

        $this->assertNotEmpty($response->json('data.token'));
    }

    #[Test]
    public function staff_cannot_login_with_invalid_username()
    {
        $response = $this->postJson('/api/staff/auth/login', [
            'username' => 'wronguser',
            'password' => 'password123'
        ]);

        $response->assertStatus(401)
                ->assertJson([
                    'success' => false,
                    'message' => 'Invalid credentials'
                ]);
    }

    #[Test]
    public function staff_cannot_login_with_invalid_password()
    {
        $response = $this->postJson('/api/staff/auth/login', [
            'username' => 'teststaff',
            'password' => 'wrongpassword'
        ]);

        $response->assertStatus(401)
                ->assertJson([
                    'success' => false,
                    'message' => 'Invalid credentials'
                ]);
    }

    #[Test]
    public function staff_login_requires_username()
    {
        $response = $this->postJson('/api/staff/auth/login', [
            'password' => 'password123'
        ]);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Validation failed'
                ])
                ->assertJsonValidationErrors(['username']);
    }

    #[Test]
    public function staff_login_requires_password()
    {
        $response = $this->postJson('/api/staff/auth/login', [
            'username' => 'teststaff'
        ]);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Validation failed'
                ])
                ->assertJsonValidationErrors(['password']);
    }

    #[Test]
    public function staff_login_deletes_old_tokens()
    {
        $staff = Staff::where('username', 'teststaff')->first();

        // Create multiple tokens
        $token1 = $staff->createToken('test1')->plainTextToken;
        $token2 = $staff->createToken('test2')->plainTextToken;

        $this->assertEquals(2, $staff->tokens()->count());

        // Login should delete old tokens
        $response = $this->postJson('/api/staff/auth/login', [
            'username' => 'teststaff',
            'password' => 'password123'
        ]);

        $response->assertStatus(200);

        // Should only have one token (the new one)
        $staff->refresh();
        $this->assertEquals(1, $staff->tokens()->count());
    }

    #[Test]
    public function authenticated_staff_can_logout()
    {
        // First login to get a token
        $loginResponse = $this->postJson('/api/staff/auth/login', [
            'username' => 'teststaff',
            'password' => 'password123'
        ]);

        $loginResponse->assertStatus(200);
        $token = $loginResponse->json('data.token');
        $this->assertNotEmpty($token);

        // Now logout with the token
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json'
        ])->postJson('/api/staff/auth/logout');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Logged out successfully'
                ]);

        // Token should be deleted
        $staff = Staff::where('username', 'teststaff')->first();
        $this->assertEquals(0, $staff->tokens()->count());
    }

    #[Test]
    public function unauthenticated_user_cannot_logout()
    {
        $response = $this->postJson('/api/staff/auth/logout');

        $response->assertStatus(401);
    }

    #[Test]
    public function logout_with_invalid_token_fails()
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer invalid-token'
        ])->postJson('/api/staff/auth/logout');

        $response->assertStatus(401);
    }

    #[Test]
    public function logout_with_revoked_token_fails()
    {
        // Login and get token
        $loginResponse = $this->postJson('/api/staff/auth/login', [
            'username' => 'teststaff',
            'password' => 'password123'
        ]);

        $token = $loginResponse->json('data.token');

        // Manually revoke the token
        $staff = Staff::where('username', 'teststaff')->first();
        $staff->tokens()->delete();

        // Try to logout with revoked token
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token
        ])->postJson('/api/staff/auth/logout');

        $response->assertStatus(401);
    }
}
