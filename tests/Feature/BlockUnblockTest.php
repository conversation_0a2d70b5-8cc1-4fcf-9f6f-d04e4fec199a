<?php

namespace Tests\Feature;

use App\Models\Blocked;
use App\Models\Staff;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class BlockUnblockTest extends TestCase
{
    use RefreshDatabase;

    protected Staff $staff;
    protected User $user;

        protected function setUp(): void
    {
        parent::setUp();

        // Create a staff member for testing
        $this->staff = Staff::factory()->withUsername('teststaff')->create();

        // Create a test user
        $this->user = User::factory()->create([
            'tele_id' => '123456789',
            'name' => 'Test User',
        ]);
    }

    #[Test]
    public function staff_can_block_user()
    {
        $token = $this->staff->createToken('test-token')->plainTextToken;

        $payload = [
            'user_id' => $this->user->id,
            'reason' => 'Violation of terms of service'
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/admin/users/block', $payload);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'block_id',
                        'user' => ['id', 'name', 'tele_id'],
                        'reason',
                        'blocked_at',
                        'blocked_by' => ['staff_id', 'staff_name']
                    ]
                ])
                ->assertJson([
                    'success' => true,
                    'message' => 'User blocked successfully',
                    'data' => [
                        'user' => [
                            'id' => $this->user->id,
                            'name' => $this->user->name,
                            'tele_id' => $this->user->tele_id,
                        ],
                        'reason' => 'Violation of terms of service',
                                                 'blocked_by' => [
                             'staff_id' => $this->staff->id,
                             'staff_name' => $this->staff->username
                         ]
                    ]
                ]);

        // Verify block record was created
        $this->assertDatabaseHas('blocked', [
            'user_id' => $this->user->id,
            'staff_id' => $this->staff->id,
            'reason' => 'Violation of terms of service'
        ]);
    }

    #[Test]
    public function staff_can_unblock_user()
    {
        // First block the user
        $blocked = Blocked::factory()
            ->forUserAndStaff($this->user, $this->staff)
            ->withReason('Test block')
            ->create();

        $token = $this->staff->createToken('test-token')->plainTextToken;

        $payload = [
            'user_id' => $this->user->id
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/admin/users/unblock', $payload);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'user' => ['id', 'name', 'tele_id'],
                        'unblocked_at',
                        'unblocked_by' => ['staff_id', 'staff_name'],
                        'previous_block'
                    ]
                ])
                ->assertJson([
                    'success' => true,
                    'message' => 'User unblocked successfully'
                ]);

        // Verify block record was deleted
        $this->assertDatabaseMissing('blocked', [
            'user_id' => $this->user->id
        ]);
    }

    #[Test]
    public function cannot_block_user_without_staff_authentication()
    {
        $payload = [
            'user_id' => $this->user->id,
            'reason' => 'Test reason'
        ];

        $response = $this->postJson('/api/admin/users/block', $payload);

        $response->assertStatus(401)
                ->assertJson([
                    'success' => false,
                    'message' => 'Authorization token required'
                ]);
    }

    #[Test]
    public function cannot_block_user_with_invalid_token()
    {
        $payload = [
            'user_id' => $this->user->id,
            'reason' => 'Test reason'
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer invalid-token',
        ])->postJson('/api/admin/users/block', $payload);

        $response->assertStatus(401)
                ->assertJson([
                    'success' => false,
                    'message' => 'Invalid or expired admin token'
                ]);
    }

    #[Test]
    public function cannot_block_already_blocked_user()
    {
        // First block the user
        Blocked::factory()
            ->forUserAndStaff($this->user, $this->staff)
            ->withReason('Already blocked')
            ->create();

        $token = $this->staff->createToken('test-token')->plainTextToken;

        $payload = [
            'user_id' => $this->user->id,
            'reason' => 'Second block attempt'
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/admin/users/block', $payload);

        $response->assertStatus(409)
                ->assertJson([
                    'success' => false,
                    'message' => 'User is already blocked'
                ]);
    }

    #[Test]
    public function cannot_unblock_user_that_is_not_blocked()
    {
        $token = $this->staff->createToken('test-token')->plainTextToken;

        $payload = [
            'user_id' => $this->user->id
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/admin/users/unblock', $payload);

        $response->assertStatus(404)
                ->assertJson([
                    'success' => false,
                    'message' => 'User is not blocked'
                ]);
    }

    #[Test]
    public function blocked_user_can_still_sync()
    {
        // Block the user
        Blocked::factory()
            ->forUserAndStaff($this->user, $this->staff)
            ->withReason('Spam behavior')
            ->create();

        $payload = [
            'telegram_id' => $this->user->tele_id,
            'name' => 'Test User',
            'country' => 'US',
            'language_code' => 'en'
        ];

        $response = $this->postJson('/api/users/sync', $payload);

        // Sync endpoint is public and doesn't check blocking - users can still sync
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'User restored successfully'
                ]);
    }

    #[Test]
    public function new_user_can_sync_even_if_no_existing_user()
    {
        // Test that a completely new user (not in database) can sync
        $payload = [
            'telegram_id' => '999999999',
            'name' => 'New User',
            'country' => 'CA',
            'language_code' => 'fr'
        ];

        $response = $this->postJson('/api/users/sync', $payload);

        $response->assertStatus(201)
                ->assertJson([
                    'success' => true,
                    'message' => 'User initialized successfully'
                ]);
    }

    #[Test]
    public function staff_can_get_blocked_users_list()
    {
        // Create some blocked users
        $user2 = User::factory()->create([
            'tele_id' => '987654321',
            'name' => 'Blocked User 2',
        ]);

        Blocked::factory()
            ->forUserAndStaff($this->user, $this->staff)
            ->withReason('Reason 1')
            ->create();

        Blocked::factory()
            ->forUserAndStaff($user2, $this->staff)
            ->withReason('Reason 2')
            ->create();

        $token = $this->staff->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/admin/users/blocked');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'blocked_users',
                        'pagination' => [
                            'current_page',
                            'total_pages',
                            'total_items',
                            'per_page'
                        ]
                    ]
                ])
                ->assertJson([
                    'success' => true,
                    'message' => 'Blocked users retrieved successfully'
                ]);

        // Check that we have 2 blocked users in the response
        $responseData = $response->json();
        $this->assertCount(2, $responseData['data']['blocked_users']);
    }

    #[Test]
    public function block_validation_requires_user_id_and_reason()
    {
        $token = $this->staff->createToken('test-token')->plainTextToken;

        // Test missing user_id
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/admin/users/block', [
            'reason' => 'Test reason'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['user_id']);

        // Test missing reason
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/admin/users/block', [
            'user_id' => $this->user->id
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['reason']);
    }

    #[Test]
    public function unblock_validation_requires_user_id()
    {
        $token = $this->staff->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/admin/users/unblock', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['user_id']);
    }
}
