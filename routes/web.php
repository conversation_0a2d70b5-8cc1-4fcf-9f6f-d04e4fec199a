<?php

use Illuminate\Support\Facades\Route;

// Web routes disabled for API-only application
// All API routes are defined in routes/api.php

Route::fallback(function () {
    return response()->json([
        'error' => 'Route not found. This is an API-only application.',
        'message' => 'Please use /api/ prefix for all API endpoints.',
        'available_endpoints' => [
            'GET /api/health' => 'API health check',
            'GET /api/v1/*' => 'API v1 endpoints'
        ]
    ], 404);
});
