<?php

use App\Http\Controllers\Api\BlockController;
use App\Http\Controllers\Api\MachineController;
use App\Http\Controllers\Api\StaffAuthController;
use App\Http\Controllers\Api\TonPaymentController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\VipPlanController;
use App\Http\Controllers\AssessmentController;
use App\Http\Controllers\CourseController;
use App\Http\Controllers\UnitController;
use App\Http\Controllers\ImportController;
use App\Http\Controllers\GeminiController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// =============================================================================
// PUBLIC ROUTES - No authentication required
// =============================================================================

Route::prefix('public')->group(function () {
    // API Health Check
    Route::get('/health', function () {
        return response()->json([
            'status' => 'ok',
            'timestamp' => now(),
            'service' => 'backend-api'
        ]);
    });

    // Public endpoints
    Route::get('/info', function () {
        return response()->json([
            'app' => config('app.name'),
            'version' => '1.0.0',
            'endpoints' => [
                'public' => '/api/public/*',
                'auth' => '/api/auth/* (user authentication)',
                'user' => '/api/user/* (requires user token)',
                'admin' => '/api/admin/* (requires admin credentials)',
                'staff' => '/api/staff/* (requires staff token)'
            ]
        ]);
    });
});

// =============================================================================
// AUTHENTICATION ROUTES - Token-based authentication
// =============================================================================

Route::prefix('auth')->group(function () {
    // User authentication (no password required)

});

// =============================================================================
// USER ROUTES - User management (legacy support)
// =============================================================================

// =============================================================================
// USER ROUTES - Protected user endpoints
// =============================================================================

Route::prefix('user')->middleware('user.auth')->group(function () {

});

// =============================================================================
// ADMIN ROUTES - Public admin endpoints (no auth required)
// =============================================================================

Route::prefix('admin')->group(function () {
    // Public admin endpoints (no authentication required)
    Route::post('/login', [StaffAuthController::class, 'login']);

    // Protected admin routes (authentication required)
    Route::middleware('admin.auth')->group(function () {
        Route::post('/logout', [StaffAuthController::class, 'logout']);
        Route::get('/me', [StaffAuthController::class, 'me']);

        // Course management
        Route::apiResource('courses', CourseController::class)
            ->names('admin.courses');

        // Additional course management endpoints
        Route::prefix('courses/{course}')->group(function () {
            Route::post('/duplicate', [CourseController::class, 'duplicate'])
                ->name('admin.courses.duplicate');
        });

        // Course statistics endpoint
        Route::get('/courses-with-stats', [CourseController::class, 'withStats'])
            ->name('admin.courses.with-stats');

        // Unit management
        Route::apiResource('units', UnitController::class)
            ->names('admin.units');

        // Additional unit management endpoints
        Route::prefix('units/{unit}')->group(function () {
            Route::post('/duplicate', [UnitController::class, 'duplicate'])
                ->name('admin.units.duplicate');
            Route::patch('/move-to-course', [UnitController::class, 'moveToCourse'])
                ->name('admin.units.move-to-course');
            Route::patch('/reorder', [UnitController::class, 'reorder'])
                ->name('admin.units.reorder');
            Route::delete('/assessments', [UnitController::class, 'clearAssessments'])
                ->name('admin.units.assessments.clear');
        });

        // Course-specific unit endpoints
        Route::get('/courses/{courseId}/units', [UnitController::class, 'getByCourse'])
            ->name('admin.courses.units');

        // Unit-specific import functionality
        Route::prefix('units/{unit}')->group(function () {
            Route::post('/import/csv', [ImportController::class, 'importCsv'])
                ->name('admin.units.import.csv');
        });

        // Global import functionality
        Route::prefix('import')->group(function () {
            Route::get('/template', [ImportController::class, 'downloadTemplate'])
                ->name('admin.import.template');
        });

        // Assessment management with unified controller
        Route::prefix('assessments')->group(function () {
            // Create assessment (type specified in request body)
            Route::post('/', [AssessmentController::class, 'store'])
                ->name('admin.assessments.store');

            // Update assessment by type and ID
            Route::put('{type}/{id}', [AssessmentController::class, 'update'])
                ->name('admin.assessments.update');

            // Delete assessment by type and ID
            Route::delete('{type}/{id}', [AssessmentController::class, 'destroy'])
                ->name('admin.assessments.destroy');

            // Attach/detach units
            Route::post('{type}/{id}/attach-units', [AssessmentController::class, 'attachToUnits'])
                ->name('admin.assessments.attach-units');
            Route::post('{type}/{id}/detach-units', [AssessmentController::class, 'detachFromUnits'])
                ->name('admin.assessments.detach-units');
        });

        // AI/Gemini functionality
        Route::prefix('ai')->group(function () {
            // AI service status
            Route::get('/status', [GeminiController::class, 'status'])
                ->name('admin.ai.status');

            // Question generation
            Route::post('/units/{unit}/generate-questions', [GeminiController::class, 'generateQuestions'])
                ->name('admin.ai.generate-questions');
            Route::post('/generate-question', [GeminiController::class, 'generateSingleQuestion'])
                ->name('admin.ai.generate-single-question');

            // Content enhancement
            Route::post('/courses/{course}/enhance-description', [GeminiController::class, 'enhanceCourseDescription'])
                ->name('admin.ai.enhance-course-description');
            Route::post('/units/{unit}/enhance-description', [GeminiController::class, 'enhanceUnitDescription'])
                ->name('admin.ai.enhance-unit-description');

            // Learning support
            Route::get('/units/{unit}/learning-objectives', [GeminiController::class, 'generateLearningObjectives'])
                ->name('admin.ai.learning-objectives');
            Route::get('/units/{unit}/study-tips', [GeminiController::class, 'generateStudyTips'])
                ->name('admin.ai.study-tips');

            // Content analysis
            Route::post('/suggest-difficulty', [GeminiController::class, 'suggestDifficulty'])
                ->name('admin.ai.suggest-difficulty');
            Route::post('/translate', [GeminiController::class, 'translateContent'])
                ->name('admin.ai.translate');
        });
    });
});

// =============================================================================
// PUBLIC ASSESSMENT ROUTES - For quiz taking (no auth required for now)
// =============================================================================

Route::prefix('public')->group(function () {
    // Course viewing endpoints (read-only)
    Route::get('/courses', [CourseController::class, 'publicList'])
        ->name('public.courses.index');
    Route::get('/courses/{course}', [CourseController::class, 'show'])
        ->name('public.courses.show');

    // Unit viewing endpoints (read-only)
    Route::get('/units/{unit}', [UnitController::class, 'show'])
        ->name('public.units.show');
    Route::get('/courses/{courseId}/units', [UnitController::class, 'getByCoursePublic'])
        ->name('public.courses.units');

    // Quiz presentation endpoints (read-only)
    // Note: Public show routes will need to be handled by original controllers or updated separately
    // as they require different logic for public access
});
