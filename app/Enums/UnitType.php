<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

/**
 * @method static static AI_GAP_FILL_SENTENCE()
 * @method static static MULTIPLE_SELECT()
 */
final class UnitType extends Enum
{
    const AI_GAP_FILL_SENTENCE = 'ai_gap_fill_sentence';
    const MULTIPLE_SELECT = 'multiple_select';

    /**
     * Get all available unit types with descriptions
     */
    public static function getTypesWithDescriptions(): array
    {
        return [
            self::AI_GAP_FILL_SENTENCE => self::getDescription(self::AI_GAP_FILL_SENTENCE),
            self::MULTIPLE_SELECT => self::getDescription(self::MULTIPLE_SELECT),
        ];
    }

    /**
     * Get the corresponding assessment model class for this unit type
     */
    public static function getAssessmentModelClass($value): string
    {
        switch ($value) {
            case self::AI_GAP_FILL_SENTENCE:
                return \App\Models\AssessmentAiGapFillSentence::class;
            case self::MULTIPLE_SELECT:
                return \App\Models\AssessmentMultipleSelect::class;
            default:
                throw new \InvalidArgumentException("Unknown unit type: {$value}");
        }
    }

    /**
     * Get the unit type from an assessment model class
     */
    public static function fromAssessmentModelClass(string $modelClass): string
    {
        switch ($modelClass) {
            case \App\Models\AssessmentAiGapFillSentence::class:
                return self::AI_GAP_FILL_SENTENCE;
            case \App\Models\AssessmentMultipleSelect::class:
                return self::MULTIPLE_SELECT;
            default:
                throw new \InvalidArgumentException("Unknown assessment model class: {$modelClass}");
        }
    }
}
