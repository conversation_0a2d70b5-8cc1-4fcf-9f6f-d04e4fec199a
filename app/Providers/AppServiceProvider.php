<?php

namespace App\Providers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\ServiceProvider;
use Illuminate\Validation\ValidationException;
use Laravel\Sanctum\Sanctum;
use Lara<PERSON>\Sanctum\PersonalAccessToken;
use Illuminate\Support\Str;
use Illuminate\Auth\Events\Lockout;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Auth\Notifications\VerifyEmail;
use Illuminate\Cache\RateLimiting\Limit;


class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    public function boot(): void
    {
        RateLimiter::for('api', static function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });

        RateLimiter::for('verification-notification', static function (Request $request) {
            return Limit::perMinute(1)->by($request->user()?->email ?: $request->ip());
        });

        RateLimiter::for('uploads', static function (Request $request) {
            return $request->user()?->hasRole('admin')
                ? Limit::none()
                : Limit::perMinute(10)->by($request->ip());
        });

        RateLimiter::for('login', static function (Request $request) {
            return Limit::perMinute(5)
                ->by(Str::transliterate(implode('|', [
                    strtolower($request->input('email')),
                    $request->ip()
                ])))
                ->response(static function (Request $request, array $headers): void {
                    event(new Lockout($request));

                    throw ValidationException::withMessages([
                        'email' => trans('auth.throttle', [
                            'seconds' => $headers['Retry-After'],
                            'minutes' => ceil($headers['Retry-After'] / 60),
                        ]),
                    ]);
                });
        });

        ResetPassword::createUrlUsing(static function (object $notifiable, string $token) {
            return config('app.frontend_url') . '/auth/reset/' . $token . '?email=' . $notifiable->getEmailForPasswordReset();
        });

        VerifyEmail::createUrlUsing(static function (object $notifiable) {
            $url = url()->temporarySignedRoute(
                'verification.verify',
                now()->addMinutes(config('auth.verification.expire', 60)),
                [
                    'ulid' => $notifiable->ulid,
                    'hash' => sha1($notifiable->getEmailForVerification()),
                ]
            );

            return config('app.frontend_url') . '/auth/verify?verify_url=' . urlencode($url);
        });

        /**
         * Remove all special characters from a string
         */
        Str::macro('onlyWords', static function (string $text): string {
            // \p{L} matches any kind of letter from any language
            // \d matches a digit in any script
            return Str::replaceMatches('/[^\p{L}\d ]/u', '', $text);
        });

        Request::macro('deviceName', function (): string {
            $device = $this->device();

            return implode(' / ', array_filter([
                trim(implode(' ', [$device->getOs('name'), $device->getOs('version')])),
                trim(implode(' ', [$device->getClient('name'), $device->getClient('version')])),
            ])) ?? 'Unknown';
        });

        Sanctum::usePersonalAccessTokenModel(
            PersonalAccessToken::class
        );
    }
}
