<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateCourseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => [
                'sometimes',
                'required',
                'string',
                'max:255',
                Rule::unique('courses', 'title')->ignore($this->route('course')?->id)
            ],
            'description' => [
                'sometimes',
                'nullable',
                'string',
                'max:2000'
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Course title is required',
            'title.unique' => 'A course with this title already exists',
            'title.max' => 'Course title cannot exceed 255 characters',
            'description.max' => 'Course description cannot exceed 2000 characters',
        ];
    }
}