<?php

namespace App\Http\Requests;

use App\Enums\Difficulty;
use App\Enums\SkillType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreUnitRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add authorization logic here if needed
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'course_id' => 'required|integer|exists:courses,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:2000',
            'skill_type' => ['required', Rule::enum(SkillType::class)],
            'difficulty' => ['required', Rule::enum(Difficulty::class)],
            'unit_type' => ['required', Rule::enum(\App\Enums\UnitType::class)],
            'unit_order' => 'nullable|integer|min:1',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // $courseId = $this->input('course_id');
            // $unitOrder = $this->input('unit_order');

            // // Validate unique unit_order within course if provided
            // if ($courseId && $unitOrder) {
            //     $exists = \App\Models\Unit::where('course_id', $courseId)
            //         ->where('unit_order', $unitOrder)
            //         ->exists();

            //     if ($exists) {
            //         $validator->errors()->add(
            //             'unit_order',
            //             'A unit with this order already exists in the course.'
            //         );
            //     }
            // }
        });
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'course_id.required' => 'The course is required.',
            'course_id.exists' => 'The selected course does not exist.',
            'title.required' => 'The unit title is required.',
            'title.max' => 'The unit title may not be greater than 255 characters.',
            'description.max' => 'The description may not be greater than 2000 characters.',
            'skill_type.required' => 'The skill type is required.',
            'difficulty.required' => 'The difficulty level is required.',
            'unit_order.min' => 'The unit order must be at least 1.',
        ];
    }

    /**
     * Get custom attribute names for validator errors.
     */
    public function attributes(): array
    {
        return [
            'course_id' => 'course',
            'skill_type' => 'skill type',
            'unit_order' => 'unit order',
        ];
    }
}
