<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateAssessmentAiGapFillSentenceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add authorization logic here if needed
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'question' => 'sometimes|required|string|max:1000',
            'context' => 'sometimes|required|string|max:2000',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $question = $this->input('question');
            $context = $this->input('context');
            
            // Validate question contains underscores if provided
            if ($question && !preg_match('/_{1,}/', $question)) {
                $validator->errors()->add(
                    'question',
                    'The question must contain at least one gap marker (____) using underscores.'
                );
            } elseif ($question) {
                // Count the number of gaps for validation
                preg_match_all('/_{1,}/', $question, $matches);
                $gapCount = count($matches[0]);
                
                // Optional: Add validation for maximum number of gaps
                if ($gapCount > 10) {
                    $validator->errors()->add(
                        'question',
                        'The question cannot contain more than 10 gaps.'
                    );
                }
            }

            // Validate that context is not empty after trimming if provided
            if ($context && empty(trim($context))) {
                $validator->errors()->add(
                    'context',
                    'The context field cannot be empty or contain only whitespace.'
                );
            }
        });
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'question.required' => 'The question field is required.',
            'question.max' => 'The question may not be greater than 1000 characters.',
            'context.required' => 'The context field is required.',
            'context.max' => 'The context may not be greater than 2000 characters.',
        ];
    }
}