<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreAssessmentAiGapFillSentenceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add authorization logic here if needed
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'question' => 'required|string|max:1000',
            'context' => 'required|string|max:2000',
            'unit_attachments' => 'nullable|array',
            'unit_attachments.*.unit_id' => 'required_with:unit_attachments|integer|exists:units,id',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $question = $this->input('question', '');
            $context = $this->input('context', '');

            // Validate that question contains at least one underscore sequence
            if (!preg_match('/_{1,}/', $question)) {
                $validator->errors()->add(
                    'question',
                    'The question must contain at least one gap marker (____) using underscores.'
                );
            } else {
                // Count the number of gaps
                preg_match_all('/_{1,}/', $question, $matches);
                $gapCount = count($matches[0]);
                
                // Optional: Add validation for maximum number of gaps
                if ($gapCount > 10) {
                    $validator->errors()->add(
                        'question',
                        'The question cannot contain more than 10 gaps.'
                    );
                }
                
                // Add informational message about detected gaps
                if ($gapCount > 1) {
                    // This doesn't add an error, just informational
                    $this->merge(['detected_gaps' => $gapCount]);
                }
            }

            // Validate that context is not empty after trimming
            if (empty(trim($context))) {
                $validator->errors()->add(
                    'context',
                    'The context field cannot be empty or contain only whitespace.'
                );
            }
        });
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'question.required' => 'The question field is required.',
            'question.max' => 'The question may not be greater than 1000 characters.',
            'context.required' => 'The context field is required.',
            'context.max' => 'The context may not be greater than 2000 characters.',
        ];
    }
}