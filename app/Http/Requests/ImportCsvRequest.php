<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ImportCsvRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add authorization logic here if needed
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'file' => 'required|file|mimes:csv,txt|max:10240', // 10MB max
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $file = $this->file('file');
            
            if ($file) {
                // Additional file validation
                $extension = strtolower($file->getClientOriginalExtension());
                
                if (!in_array($extension, ['csv', 'txt'])) {
                    $validator->errors()->add('file', 'File must be a CSV file with .csv or .txt extension.');
                }
                
                // Check if file is readable
                if (!$file->isValid()) {
                    $validator->errors()->add('file', 'The uploaded file is not valid or corrupted.');
                }
                
                // Basic CSV structure validation (check if it has comma-separated content)
                if ($file->isValid()) {
                    $handle = fopen($file->getRealPath(), 'r');
                    if ($handle) {
                        $firstLine = fgetcsv($handle, 1000, ',');
                        fclose($handle);
                        
                        if (!$firstLine || count($firstLine) < 4) {
                            $validator->errors()->add('file', 'CSV file must contain valid comma-separated data with at least 4 columns.');
                        }
                    } else {
                        $validator->errors()->add('file', 'Unable to read the uploaded file.');
                    }
                }
            }
        });
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'file.required' => 'A CSV file is required for import.',
            'file.file' => 'The uploaded item must be a file.',
            'file.mimes' => 'The file must be a CSV file (.csv or .txt extension).',
            'file.max' => 'The file size must not exceed 10MB.',
        ];
    }

    /**
     * Get custom attribute names for validator errors.
     */
    public function attributes(): array
    {
        return [
            'file' => 'CSV file',
        ];
    }

    /**
     * Get the validated file
     */
    public function getFile(): \Illuminate\Http\UploadedFile
    {
        return $this->validated()['file'];
    }
}