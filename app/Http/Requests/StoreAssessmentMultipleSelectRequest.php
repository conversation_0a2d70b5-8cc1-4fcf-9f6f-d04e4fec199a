<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreAssessmentMultipleSelectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add authorization logic here if needed
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'question' => 'required|string|max:1000',
            'answer_list' => 'required|array|min:2|max:10',
            'answer_list.*' => 'required|string|max:500',
            'correct_answer_indexes' => 'required|array|min:1',
            'correct_answer_indexes.*' => 'required|integer|min:0',
            'explanations' => 'nullable|array',
            'explanations.*' => 'nullable|string|max:1000',
            'unit_attachments' => 'nullable|array',
            'unit_attachments.*.unit_id' => 'required_with:unit_attachments|integer|exists:units,id',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $answerList = $this->input('answer_list', []);
            $correctIndexes = $this->input('correct_answer_indexes', []);

            // Validate that correct answer indexes are within bounds
            foreach ($correctIndexes as $index) {
                if ($index >= count($answerList)) {
                    $validator->errors()->add(
                        'correct_answer_indexes',
                        'Correct answer index ' . $index . ' is out of bounds for the answer list.'
                    );
                }
            }

            // Validate that correct answer indexes are unique
            if (count($correctIndexes) !== count(array_unique($correctIndexes))) {
                $validator->errors()->add(
                    'correct_answer_indexes',
                    'Correct answer indexes must be unique.'
                );
            }
        });
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'question.required' => 'The question field is required.',
            'question.max' => 'The question may not be greater than 1000 characters.',
            'answer_list.required' => 'At least 2 answer options are required.',
            'answer_list.min' => 'At least 2 answer options are required.',
            'answer_list.max' => 'No more than 10 answer options are allowed.',
            'answer_list.*.required' => 'Each answer option is required.',
            'answer_list.*.max' => 'Each answer option may not be greater than 500 characters.',
            'correct_answer_indexes.required' => 'At least one correct answer must be specified.',
            'correct_answer_indexes.min' => 'At least one correct answer must be specified.',
            'correct_answer_indexes.*.integer' => 'Correct answer indexes must be integers.',
            'correct_answer_indexes.*.min' => 'Correct answer indexes must be 0 or greater.',
        ];
    }
}
