<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

use Symfony\Component\HttpFoundation\Response;

class UserAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {

        // Check if user has valid token
        if (!$request->bearerToken()) {
            return response()->json([
                'success' => false,
                'message' => 'User token required'
            ], 401);
        }

        // TODO: Implement your named user authentication logic here
        // Example logic structure:
        // 1. Validate token
        // 2. Get user from token (by hash)
        // 3. Check if user is named user type
        // 4. Return unauthorized if not valid user

        // Placeholder for your custom logic
        $isValidUser = $this->checkUserToken($request);

        if (!$isValidUser) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid user token'
            ], 403);
        }

        return $next($request);
    }

    /**
     * Check user token - implement your logic here
     */
    private function checkUserToken(Request $request): bool
    {
        // TODO: Implement your user token checking logic
        // Examples:
        // - Validate token against named users
        // - Check user hash from token
        // - Verify user is active/valid

        return false; // Placeholder - replace with your logic (set to false for testing)
    }
}
