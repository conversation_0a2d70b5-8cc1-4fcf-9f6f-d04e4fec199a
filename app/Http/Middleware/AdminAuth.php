<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Staff;

class AdminAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if bearer token is present
        if (!$request->bearerToken()) {
            return response()->json([
                'success' => false,
                'message' => 'Authorization token required'
            ], 401);
        }

        // Get authenticated staff user using the staff guard
        $staff = $request->user('staff');

        if (!$staff) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid or expired admin token'
            ], 401);
        }

        if (!($staff instanceof Staff)) {
            return response()->json([
                'success' => false,
                'message' => 'Admin access required'
            ], 403);
        }

        return $next($request);
    }
}
