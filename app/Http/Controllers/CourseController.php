<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCourseRequest;
use App\Http\Requests\UpdateCourseRequest;
use App\Models\Course;
use App\Services\CourseService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CourseController extends Controller
{
    public function __construct(
        private CourseService $service
    ) {}

    /**
     * Display a listing of courses
     */
    public function index(Request $request): JsonResponse
    {
        $query = Course::with(['units'])
            ->orderBy('created_at', 'desc');

        $perPage = min($request->get('per_page', 15), 100);
        $courses = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'message' => 'Courses retrieved successfully',
            'data' => $courses
        ]);
    }

    public function publicList(): JsonResponse
    {
        $courses = Course::with(['units' => function ($query) {
            $query->select('id', 'course_id', 'title', 'skill_type', 'difficulty')
                  ->with(['assessments'])
                  ->whereHas('assessments')
                  ->orderBy('unit_order');
        }])
        ->whereHas('units')
        ->orderBy('created_at', 'desc')
        ->get();

        return response()->json([
            'success' => true,
            'message' => 'Courses retrieved successfully',
            'data' => $courses
        ]);
    }

    /**
     * Store a newly created course
     */
    public function store(StoreCourseRequest $request): JsonResponse
    {
        try {
            $course = $this->service->create($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Course created successfully',
                'data' => $course->load('units')
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create course',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified course
     */
    public function show(Course $course): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => 'Course retrieved successfully',
            'data' => $course->load(['units.assessments'])
        ]);
    }

    /**
     * Update the specified course
     */
    public function update(UpdateCourseRequest $request, Course $course): JsonResponse
    {
        try {
            $updatedCourse = $this->service->update($course, $request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Course updated successfully',
                'data' => $updatedCourse->load('units')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update course',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified course
     */
    public function destroy(Course $course): JsonResponse
    {
        try {
            $this->service->delete($course);

            return response()->json([
                'success' => true,
                'message' => 'Course deleted successfully',
                'data' => null
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete course',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Duplicate an existing course
     */
    public function duplicate(Course $course): JsonResponse
    {
        try {
            $duplicatedCourse = $this->service->duplicate($course);

            return response()->json([
                'success' => true,
                'message' => 'Course duplicated successfully',
                'data' => $duplicatedCourse->load(['units.assessments'])
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to duplicate course',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get courses with unit count statistics
     */
    public function withStats(): JsonResponse
    {
        $courses = Course::withCount('units')
            ->with(['units' => function ($query) {
                $query->select('id', 'course_id', 'title', 'skill_type', 'difficulty')
                      ->orderBy('unit_order');
            }])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'message' => 'Courses with statistics retrieved successfully',
            'data' => $courses
        ]);
    }
}
