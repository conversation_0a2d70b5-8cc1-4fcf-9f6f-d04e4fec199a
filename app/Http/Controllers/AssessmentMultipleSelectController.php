<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreAssessmentMultipleSelectRequest;
use App\Http\Requests\UpdateAssessmentMultipleSelectRequest;
use App\Models\AssessmentMultipleSelect;
use App\Services\AssessmentMultipleSelectService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AssessmentMultipleSelectController extends Controller
{
    public function __construct(
        private AssessmentMultipleSelectService $service
    ) {}

    /**
     * Display a listing of the assessments with pagination
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $perPage = min($perPage, 100); // Limit to 100 items per page

        $assessments = AssessmentMultipleSelect::with('assessment.units')
            ->paginate($perPage);

        return response()->json([
            'success' => true,
            'message' => 'Assessments retrieved successfully',
            'data' => $assessments
        ]);
    }

    /**
     * Display the specified assessment
     */
    public function show(AssessmentMultipleSelect $assessmentMultipleSelect): JsonResponse
    {
        $assessmentMultipleSelect->load('assessment.units');
        return response()->json([
            'success' => true,
            'message' => 'Assessment retrieved successfully',
            'data' => $assessmentMultipleSelect
        ]);
    }

    /**
     * Store a newly created assessment
     */
    public function store(StoreAssessmentMultipleSelectRequest $request): JsonResponse
    {
        try {
            $assessment = $this->service->create($request->validated());

            // Handle unit attachments if provided
            if ($request->has('unit_attachments')) {
                $this->service->attachToUnits($assessment, $request->input('unit_attachments'));
                $assessment = $assessment->fresh(['assessment.units']);
            }

            return response()->json([
                'success' => true,
                'message' => 'Assessment created successfully',
                'data' => $assessment
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create assessment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified assessment
     */
    public function update(
        UpdateAssessmentMultipleSelectRequest $request,
        AssessmentMultipleSelect $assessmentMultipleSelect
    ): JsonResponse {
        try {
            $assessment = $this->service->update($assessmentMultipleSelect, $request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Assessment updated successfully',
                'data' => $assessment
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update assessment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified assessment
     */
    public function destroy(int $id): JsonResponse
    {
        $assessmentMultipleSelect = AssessmentMultipleSelect::findOrFail($id);
        try {
            $deleted = $this->service->delete($assessmentMultipleSelect);

            if ($deleted) {
                return response()->json([
                    'success' => true,
                    'message' => 'Assessment deleted successfully',
                    'data' => null
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete assessment'
            ], 500);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete assessment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Attach assessment to units
     */
    public function attachToUnits(
        Request $request,
        AssessmentMultipleSelect $assessmentMultipleSelect
    ): JsonResponse {
        $request->validate([
            'unit_attachments' => 'required|array',
            'unit_attachments.*.unit_id' => 'required|integer|exists:units,id',
            'unit_attachments.*.assessment_order' => 'required|integer|min:1',
        ]);

        try {
            $this->service->attachToUnits($assessmentMultipleSelect, $request->input('unit_attachments'));

            return response()->json([
                'success' => true,
                'message' => 'Assessment attached to units successfully',
                'data' => null
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to attach assessment to units',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Detach assessment from units
     */
    public function detachFromUnits(
        Request $request,
        AssessmentMultipleSelect $assessmentMultipleSelect
    ): JsonResponse {
        $request->validate([
            'unit_ids' => 'nullable|array',
            'unit_ids.*' => 'integer|exists:units,id',
        ]);

        try {
            $unitIds = $request->input('unit_ids', []);
            $this->service->detachFromUnits($assessmentMultipleSelect, $unitIds);

            return response()->json([
                'success' => true,
                'message' => 'Assessment detached from units successfully',
                'data' => null
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to detach assessment from units',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
