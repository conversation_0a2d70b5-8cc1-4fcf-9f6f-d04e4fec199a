<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\AssessmentMultipleSelect;
use App\Models\AssessmentAiGapFillSentence;
use App\Services\AssessmentMultipleSelectService;
use App\Services\AssessmentAiGapFillSentenceService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AssessmentController extends Controller
{
    private AssessmentMultipleSelectService $multipleSelectService;
    private AssessmentAiGapFillSentenceService $aiGapFillService;

    public function __construct(
        AssessmentMultipleSelectService $multipleSelectService,
        AssessmentAiGapFillSentenceService $aiGapFillService
    ) {
        $this->multipleSelectService = $multipleSelectService;
        $this->aiGapFillService = $aiGapFillService;
    }

    /**
     * Store a newly created assessment based on type
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'type' => 'required|string|in:multiple-select,ai-gap-fill-sentence'
        ]);

        $type = $request->input('type');

        try {
            if ($type === 'multiple-select') {
                return $this->storeMultipleSelect($request);
            } elseif ($type === 'ai-gap-fill-sentence') {
                return $this->storeAiGapFillSentence($request);
            }

            return response()->json([
                'success' => false,
                'message' => 'Invalid assessment type'
            ], 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create assessment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update assessment based on type
     */
    public function update(Request $request, string $type, int $id): JsonResponse
    {
        if (!in_array($type, ['multiple-select', 'ai-gap-fill-sentence'])) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid assessment type'
            ], 400);
        }

        try {
            if ($type === 'multiple-select') {
                return $this->updateMultipleSelect($request, $id);
            } elseif ($type === 'ai-gap-fill-sentence') {
                return $this->updateAiGapFillSentence($request, $id);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update assessment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete assessment based on type
     */
    public function destroy(string $type, int $id): JsonResponse
    {
        if (!in_array($type, ['multiple-select', 'ai-gap-fill-sentence'])) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid assessment type'
            ], 400);
        }

        try {
            if ($type === 'multiple-select') {
                return $this->destroyMultipleSelect($id);
            } elseif ($type === 'ai-gap-fill-sentence') {
                return $this->destroyAiGapFillSentence($id);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete assessment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Attach assessment to units
     */
    public function attachToUnits(Request $request, string $type, int $id): JsonResponse
    {
        if (!in_array($type, ['multiple-select', 'ai-gap-fill-sentence'])) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid assessment type'
            ], 400);
        }

        $request->validate([
            'unit_attachments' => 'required|array',
            'unit_attachments.*.unit_id' => 'required|integer|exists:units,id',
            'unit_attachments.*.assessment_order' => 'nullable|integer|min:1'
        ]);

        try {
            if ($type === 'multiple-select') {
                $assessment = AssessmentMultipleSelect::findOrFail($id);
                $this->multipleSelectService->attachToUnits($assessment, $request->input('unit_attachments'));
            } elseif ($type === 'ai-gap-fill-sentence') {
                $assessment = AssessmentAiGapFillSentence::findOrFail($id);
                $this->aiGapFillService->attachToUnits($assessment, $request->input('unit_attachments'));
            }

            return response()->json([
                'success' => true,
                'message' => 'Assessment attached to units successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to attach assessment to units',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Detach assessment from units
     */
    public function detachFromUnits(Request $request, string $type, int $id): JsonResponse
    {
        if (!in_array($type, ['multiple-select', 'ai-gap-fill-sentence'])) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid assessment type'
            ], 400);
        }

        $request->validate([
            'unit_ids' => 'nullable|array',
            'unit_ids.*' => 'integer|exists:units,id'
        ]);

        try {
            if ($type === 'multiple-select') {
                $assessment = AssessmentMultipleSelect::findOrFail($id);
                $this->multipleSelectService->detachFromUnits($assessment, $request->input('unit_ids', []));
            } elseif ($type === 'ai-gap-fill-sentence') {
                $assessment = AssessmentAiGapFillSentence::findOrFail($id);
                $this->aiGapFillService->detachFromUnits($assessment, $request->input('unit_ids', []));
            }

            return response()->json([
                'success' => true,
                'message' => 'Assessment detached from units successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to detach assessment from units',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle multiple select assessment creation
     */
    private function storeMultipleSelect(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'question' => 'required|string|max:1000',
            'answer_list' => 'required|array|min:2|max:10',
            'answer_list.*' => 'required|string|max:500',
            'correct_answer_indexes' => 'required|array|min:1',
            'correct_answer_indexes.*' => 'required|integer|min:0',
            'explanations' => 'nullable|array',
            'explanations.*' => 'nullable|string|max:1000',
            'unit_attachments' => 'nullable|array',
            'unit_attachments.*.unit_id' => 'required|integer|exists:units,id',
            'unit_attachments.*.assessment_order' => 'nullable|integer|min:1'
        ]);

        $assessment = $this->multipleSelectService->create($validated);

        // Handle unit attachments if provided
        if ($request->has('unit_attachments')) {
            $this->multipleSelectService->attachToUnits($assessment, $request->input('unit_attachments'));
            $assessment = $assessment->fresh(['assessment.units']);
        }

        return response()->json([
            'success' => true,
            'message' => 'Assessment created successfully',
            'data' => $assessment
        ], 201);
    }

    /**
     * Handle AI gap fill sentence assessment creation
     */
    private function storeAiGapFillSentence(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'question' => 'required|string|max:1000',
            'context' => 'required|string|max:2000',
            'unit_attachments' => 'nullable|array',
            'unit_attachments.*.unit_id' => 'required|integer|exists:units,id',
            'unit_attachments.*.assessment_order' => 'nullable|integer|min:1'
        ]);

        $assessment = $this->aiGapFillService->create($validated);

        // Handle unit attachments if provided
        if ($request->has('unit_attachments')) {
            $this->aiGapFillService->attachToUnits($assessment, $request->input('unit_attachments'));
            $assessment = $assessment->fresh(['assessment.units']);
        }

        return response()->json([
            'success' => true,
            'message' => 'Assessment created successfully',
            'data' => $assessment
        ], 201);
    }

    /**
     * Handle multiple select assessment update
     */
    private function updateMultipleSelect(Request $request, int $id): JsonResponse
    {
        $assessment = AssessmentMultipleSelect::findOrFail($id);

        $validated = $request->validate([
            'question' => 'sometimes|required|string|max:1000',
            'answer_list' => 'sometimes|required|array|min:2|max:10',
            'answer_list.*' => 'required|string|max:500',
            'correct_answer_indexes' => 'sometimes|required|array|min:1',
            'correct_answer_indexes.*' => 'required|integer|min:0',
            'explanations' => 'nullable|array',
            'explanations.*' => 'nullable|string|max:1000'
        ]);

        $assessment = $this->multipleSelectService->update($assessment, $validated);

        return response()->json([
            'success' => true,
            'message' => 'Assessment updated successfully',
            'data' => $assessment
        ]);
    }

    /**
     * Handle AI gap fill sentence assessment update
     */
    private function updateAiGapFillSentence(Request $request, int $id): JsonResponse
    {
        $assessment = AssessmentAiGapFillSentence::findOrFail($id);

        $validated = $request->validate([
            'question' => 'sometimes|required|string|max:1000',
            'context' => 'sometimes|required|string|max:2000'
        ]);

        $assessment = $this->aiGapFillService->update($assessment, $validated);

        return response()->json([
            'success' => true,
            'message' => 'Assessment updated successfully',
            'data' => $assessment
        ]);
    }

    /**
     * Handle multiple select assessment deletion
     */
    private function destroyMultipleSelect(int $id): JsonResponse
    {
        $assessment = AssessmentMultipleSelect::findOrFail($id);
        $this->multipleSelectService->delete($assessment);

        return response()->json([
            'success' => true,
            'message' => 'Assessment deleted successfully',
            'data' => null
        ]);
    }

    /**
     * Handle AI gap fill sentence assessment deletion
     */
    private function destroyAiGapFillSentence(int $id): JsonResponse
    {
        $assessment = AssessmentAiGapFillSentence::findOrFail($id);
        $this->aiGapFillService->delete($assessment);

        return response()->json([
            'success' => true,
            'message' => 'Assessment deleted successfully',
            'data' => null
        ]);
    }
}