<?php

namespace App\Http\Controllers;

use App\Http\Requests\ImportCsvRequest;
use App\Models\Unit;
use App\Services\CsvImportService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ImportController extends Controller
{
    public function __construct(
        private CsvImportService $importService
    ) {}

    /**
     * Import assessments from CSV file for a specific unit
     */
    public function importCsv(ImportCsvRequest $request, Unit $unit): JsonResponse
    {
        try {
            $result = $this->importService->importAssessmentsForUnit(
                $request->getFile(),
                $unit
            );

            return response()->json([
                'success' => true,
                'message' => 'CSV import completed successfully',
                'data' => [
                    'unit_id' => $unit->id,
                    'unit_title' => $unit->title,
                    'summary' => [
                        'assessments_created' => $result['assessments_created']
                    ],
                    'created_assessments' => array_map(function($assessment) {
                        return [
                            'id' => $assessment->id,
                            'question' => $assessment->question,
                            'answer_count' => count($assessment->answer_list)
                        ];
                    }, $result['assessments']),
                ]
            ], 201);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'message' => 'CSV validation failed',
                'error' => $e->getMessage()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to import CSV file',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download CSV template for import
     */
    public function downloadTemplate(): Response
    {
        try {
            $csvContent = $this->importService->generateTemplate();

            return response($csvContent, 200, [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="assessment_import_template.csv"',
                'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
                'Expires' => '0'
            ]);
        } catch (\Exception $e) {
            return response([
                'success' => false,
                'message' => 'Failed to generate template',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
