<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreAssessmentAiGapFillSentenceRequest;
use App\Http\Requests\UpdateAssessmentAiGapFillSentenceRequest;
use App\Models\AssessmentAiGapFillSentence;
use App\Services\AssessmentAiGapFillSentenceService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AssessmentAiGapFillSentenceController extends Controller
{
    public function __construct(
        private AssessmentAiGapFillSentenceService $service
    ) {}

    /**
     * Display a listing of the assessments with pagination
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $perPage = min($perPage, 100); // Limit to 100 items per page

        $assessments = AssessmentAiGapFillSentence::with('assessment.units')
            ->paginate($perPage);

        return response()->json([
            'success' => true,
            'message' => 'AI Gap Fill Sentence assessments retrieved successfully',
            'data' => $assessments
        ]);
    }

    /**
     * Display the specified assessment
     */
    public function show(AssessmentAiGapFillSentence $assessmentAiGapFillSentence): JsonResponse
    {
        $assessmentAiGapFillSentence->load('assessment.units');
        return response()->json([
            'success' => true,
            'message' => 'AI Gap Fill Sentence assessment retrieved successfully',
            'data' => $assessmentAiGapFillSentence
        ]);
    }

    /**
     * Store a newly created assessment
     */
    public function store(StoreAssessmentAiGapFillSentenceRequest $request): JsonResponse
    {
        try {
            $assessment = $this->service->create($request->validated());

            // Handle unit attachments if provided
            if ($request->has('unit_attachments')) {
                $this->service->attachToUnits($assessment, $request->input('unit_attachments'));
                $assessment = $assessment->fresh(['assessment.units']);
            }

            return response()->json([
                'success' => true,
                'message' => 'AI Gap Fill Sentence assessment created successfully',
                'data' => $assessment
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create AI Gap Fill Sentence assessment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified assessment
     */
    public function update(
        UpdateAssessmentAiGapFillSentenceRequest $request,
        AssessmentAiGapFillSentence $assessmentAiGapFillSentence
    ): JsonResponse {
        try {
            $assessment = $this->service->update($assessmentAiGapFillSentence, $request->validated());

            return response()->json([
                'success' => true,
                'message' => 'AI Gap Fill Sentence assessment updated successfully',
                'data' => $assessment
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update AI Gap Fill Sentence assessment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified assessment
     */
    public function destroy(int $id): JsonResponse
    {
        $assessmentAiGapFillSentence = AssessmentAiGapFillSentence::findOrFail($id);
        try {
            $deleted = $this->service->delete($assessmentAiGapFillSentence);

            if ($deleted) {
                return response()->json([
                    'success' => true,
                    'message' => 'AI Gap Fill Sentence assessment deleted successfully',
                    'data' => null
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete AI Gap Fill Sentence assessment'
            ], 500);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete AI Gap Fill Sentence assessment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Attach assessment to units
     */
    public function attachToUnits(
        Request $request,
        AssessmentAiGapFillSentence $assessmentAiGapFillSentence
    ): JsonResponse {
        $request->validate([
            'unit_attachments' => 'required|array',
            'unit_attachments.*.unit_id' => 'required|integer|exists:units,id',
            'unit_attachments.*.assessment_order' => 'required|integer|min:1',
        ]);

        try {
            $this->service->attachToUnits($assessmentAiGapFillSentence, $request->input('unit_attachments'));

            return response()->json([
                'success' => true,
                'message' => 'AI Gap Fill Sentence assessment attached to units successfully',
                'data' => null
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to attach AI Gap Fill Sentence assessment to units',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Detach assessment from units
     */
    public function detachFromUnits(
        Request $request,
        AssessmentAiGapFillSentence $assessmentAiGapFillSentence
    ): JsonResponse {
        $request->validate([
            'unit_ids' => 'nullable|array',
            'unit_ids.*' => 'integer|exists:units,id',
        ]);

        try {
            $unitIds = $request->input('unit_ids', []);
            $this->service->detachFromUnits($assessmentAiGapFillSentence, $unitIds);

            return response()->json([
                'success' => true,
                'message' => 'AI Gap Fill Sentence assessment detached from units successfully',
                'data' => null
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to detach AI Gap Fill Sentence assessment from units',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}