<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Unit;
use App\Models\AssessmentMultipleSelect;
use App\Services\GeminiService;
use App\Services\QuestionGeneratorService;
use App\Services\ContentEnhancerService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class GeminiController extends Controller
{
    protected GeminiService $geminiService;
    protected QuestionGeneratorService $questionGenerator;
    protected ContentEnhancerService $contentEnhancer;

    public function __construct(
        GeminiService $geminiService,
        QuestionGeneratorService $questionGenerator,
        ContentEnhancerService $contentEnhancer
    ) {
        $this->geminiService = $geminiService;
        $this->questionGenerator = $questionGenerator;
        $this->contentEnhancer = $contentEnhancer;
    }

    /**
     * Generate questions for a unit
     */
    public function generateQuestions(Request $request, Unit $unit): JsonResponse
    {
        $request->validate([
            'count' => 'integer|min:1|max:10',
            'create' => 'boolean',
        ]);

        $count = $request->get('count', 3);
        $create = $request->get('create', false);

        try {
            if ($create) {
                $result = $this->questionGenerator->createQuestionsForUnit($unit, $count);
                $questions = array_map(function ($item) {
                    return [
                        'assessment_id' => $item['assessment']->id,
                        'question' => $item['multiple_select']->question,
                        'answer_list' => $item['multiple_select']->answer_list,
                        'correct_answer_indexes' => $item['multiple_select']->correct_answer_indexes,
                        'explanations' => $item['multiple_select']->explanations,
                    ];
                }, $result);
            } else {
                $questions = $this->questionGenerator->generateQuestionsForUnit($unit, $count);
            }

            return response()->json([
                'success' => true,
                'message' => $create ? 'Questions generated and created successfully' : 'Questions generated successfully',
                'data' => [
                    'unit' => [
                        'id' => $unit->id,
                        'title' => $unit->title,
                        'skill_type' => $unit->skill_type->value,
                        'difficulty' => $unit->difficulty->value,
                    ],
                    'questions' => $questions,
                    'count' => count($questions),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate questions',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate a single question
     */
    public function generateSingleQuestion(Request $request): JsonResponse
    {
        $request->validate([
            'topic' => 'required|string|max:255',
            'difficulty' => ['required', Rule::in(['beginner', 'elementary', 'intermediate', 'upper_intermediate', 'advanced', 'proficient'])],
            'skill_type' => ['required', Rule::in(['vocabulary', 'grammar', 'listening', 'reading', 'speaking', 'writing'])],
        ]);

        try {
            $question = $this->questionGenerator->generateSingleQuestion(
                $request->topic,
                $request->difficulty,
                $request->skill_type
            );

            if (!$question) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to generate question',
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Question generated successfully',
                'data' => $question,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate question',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Enhance course description
     */
    public function enhanceCourseDescription(Course $course): JsonResponse
    {
        try {
            $enhancedDescription = $this->contentEnhancer->enhanceCourseDescription($course);

            if (!$enhancedDescription) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to enhance course description',
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Course description enhanced successfully',
                'data' => [
                    'course_id' => $course->id,
                    'original_description' => $course->description,
                    'enhanced_description' => $enhancedDescription,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to enhance course description',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Enhance unit description
     */
    public function enhanceUnitDescription(Unit $unit): JsonResponse
    {
        try {
            $enhancedDescription = $this->contentEnhancer->enhanceUnitDescription($unit);

            if (!$enhancedDescription) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to enhance unit description',
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Unit description enhanced successfully',
                'data' => [
                    'unit_id' => $unit->id,
                    'original_description' => $unit->description,
                    'enhanced_description' => $enhancedDescription,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to enhance unit description',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate learning objectives for a unit
     */
    public function generateLearningObjectives(Unit $unit): JsonResponse
    {
        try {
            $objectives = $this->contentEnhancer->generateLearningObjectives($unit);

            if (!$objectives) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to generate learning objectives',
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Learning objectives generated successfully',
                'data' => [
                    'unit_id' => $unit->id,
                    'unit_title' => $unit->title,
                    'objectives' => $objectives,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate learning objectives',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate study tips for a unit
     */
    public function generateStudyTips(Unit $unit): JsonResponse
    {
        try {
            $tips = $this->contentEnhancer->generateStudyTips($unit);

            if (!$tips) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to generate study tips',
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Study tips generated successfully',
                'data' => [
                    'unit_id' => $unit->id,
                    'unit_title' => $unit->title,
                    'tips' => $tips,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate study tips',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Suggest difficulty level for content
     */
    public function suggestDifficulty(Request $request): JsonResponse
    {
        $request->validate([
            'content' => 'required|string',
            'content_type' => 'string|in:text,question,description',
        ]);

        try {
            $difficulty = $this->contentEnhancer->suggestDifficultyLevel(
                $request->content,
                $request->get('content_type', 'text')
            );

            if (!$difficulty) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to suggest difficulty level',
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Difficulty level suggested successfully',
                'data' => [
                    'content' => $request->content,
                    'suggested_difficulty' => trim($difficulty),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to suggest difficulty level',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Translate content
     */
    public function translateContent(Request $request): JsonResponse
    {
        $request->validate([
            'content' => 'required|string',
            'target_language' => 'required|string|max:50',
        ]);

        try {
            $translation = $this->contentEnhancer->translateContent(
                $request->content,
                $request->target_language
            );

            if (!$translation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to translate content',
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Content translated successfully',
                'data' => [
                    'original_content' => $request->content,
                    'target_language' => $request->target_language,
                    'translated_content' => $translation,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to translate content',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get AI service status
     */
    public function status(): JsonResponse
    {
        try {
            // Test basic connectivity
            $testResult = $this->geminiService->generateContent('Hello', ['max_tokens' => 10]);
            
            return response()->json([
                'success' => true,
                'message' => 'Gemini AI service is operational',
                'data' => [
                    'status' => 'operational',
                    'test_response' => $testResult ? 'success' : 'failed',
                    'timestamp' => now()->toISOString(),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gemini AI service is not available',
                'data' => [
                    'status' => 'error',
                    'error' => $e->getMessage(),
                    'timestamp' => now()->toISOString(),
                ],
            ], 503);
        }
    }
}
