<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreUnitRequest;
use App\Http\Requests\UpdateUnitRequest;
use App\Models\Unit;
use App\Services\UnitService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class UnitController extends Controller
{
    public function __construct(
        private UnitService $service
    ) {}

    /**
     * Transform unit data to use direct assessment format
     */
    private function transformUnitData(Unit $unit): array
    {
        $unitData = $unit->toArray();
        $unitData['assessments'] = $unit->getDirectAssessments()->toArray();
        return $unitData;
    }

    /**
     * Transform pagination data with direct assessments
     */
    private function transformPaginationData($units)
    {
        $paginationData = $units->toArray();
        $paginationData['data'] = collect($units->items())->map(function (Unit $unit) {
            return $this->transformUnitData($unit);
        })->toArray();
        return $paginationData;
    }

    /**
     * Display a listing of units with optional course filtering
     */
    public function index(Request $request): JsonResponse
    {
        $query = Unit::with(['course', 'assessments'])
            ->orderBy('course_id')
            ->orderBy('unit_order');

        // Filter by course if provided
        if ($request->has('course_id')) {
            $query->where('course_id', $request->get('course_id'));
        }

        // Filter by skill type if provided
        if ($request->has('skill_type')) {
            $query->where('skill_type', $request->get('skill_type'));
        }

        // Filter by difficulty if provided
        if ($request->has('difficulty')) {
            $query->where('difficulty', $request->get('difficulty'));
        }

        $perPage = min($request->get('per_page', 15), 100);
        $units = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'message' => 'Units retrieved successfully',
            'data' => $this->transformPaginationData($units)
        ]);
    }

    /**
     * Display the specified unit
     */
    public function show(Unit $unit): JsonResponse
    {
        $unit->load(['course', 'assessments']);

        return response()->json([
            'success' => true,
            'message' => 'Unit retrieved successfully',
            'data' => $this->transformUnitData($unit)
        ]);
    }

    /**
     * Store a newly created unit
     */
    public function store(StoreUnitRequest $request): JsonResponse
    {
        try {
            $unit = $this->service->create($request->validated());
            $unit->load(['course', 'assessments']);

            return response()->json([
                'success' => true,
                'message' => 'Unit created successfully',
                'data' => $this->transformUnitData($unit)
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create unit',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified unit
     */
    public function update(UpdateUnitRequest $request, Unit $unit): JsonResponse
    {
        try {
            $updatedUnit = $this->service->update($unit, $request->validated());
            $updatedUnit->load(['course', 'assessments']);

            return response()->json([
                'success' => true,
                'message' => 'Unit updated successfully',
                'data' => $this->transformUnitData($updatedUnit)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update unit',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified unit
     */
    public function destroy(Unit $unit): JsonResponse
    {
        try {
            $deleted = $this->service->delete($unit);

            if ($deleted) {
                return response()->json([
                    'success' => true,
                    'message' => 'Unit deleted successfully',
                    'data' => null
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete unit'
            ], 500);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete unit',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Duplicate an existing unit
     */
    public function duplicate(Request $request, Unit $unit): JsonResponse
    {
        $request->validate([
            'target_order' => 'nullable|integer|min:1',
        ]);

        try {
            $duplicatedUnit = $this->service->duplicate($unit, $request->input('target_order'));
            $duplicatedUnit->load(['course', 'assessments']);

            return response()->json([
                'success' => true,
                'message' => 'Unit duplicated successfully',
                'data' => $this->transformUnitData($duplicatedUnit)
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to duplicate unit',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Move unit to different course
     */
    public function moveToCourse(Request $request, Unit $unit): JsonResponse
    {
        $request->validate([
            'target_course_id' => 'required|integer|exists:courses,id',
            'target_order' => 'nullable|integer|min:1',
        ]);

        try {
            $movedUnit = $this->service->moveToCourse(
                $unit,
                $request->input('target_course_id'),
                $request->input('target_order')
            );
            $movedUnit->load(['course', 'assessments']);

            return response()->json([
                'success' => true,
                'message' => 'Unit moved to course successfully',
                'data' => $this->transformUnitData($movedUnit)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to move unit to course',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reorder units within a course
     */
    public function reorder(Request $request, Unit $unit): JsonResponse
    {
        $request->validate([
            'new_order' => 'required|integer|min:1',
            'course_id' => 'nullable|integer|exists:courses,id',
        ]);

        try {
            $courseId = $request->input('course_id', $unit->course_id);
            $this->service->reorderUnits($unit, $request->input('new_order'), $courseId);

            // Update the unit's order
            $unit->update(['unit_order' => $request->input('new_order')]);
            $unit->load(['course', 'assessments']);

            return response()->json([
                'success' => true,
                'message' => 'Unit reordered successfully',
                'data' => $this->transformUnitData($unit)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reorder unit',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get units by course
     */
    public function getByCourse(Request $request, int $courseId): JsonResponse
    {
        $perPage = min($request->get('per_page', 50), 100);

        $query = Unit::with(['assessments'])
            ->where('course_id', $courseId)
            ->orderBy('unit_order');

        // Filter by skill type if provided
        if ($request->has('skill_type')) {
            $query->where('skill_type', $request->get('skill_type'));
        }

        // Filter by difficulty if provided
        if ($request->has('difficulty')) {
            $query->where('difficulty', $request->get('difficulty'));
        }

        $units = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'message' => 'Course units retrieved successfully',
            'data' => $this->transformPaginationData($units)
        ]);
    }

    public function getByCoursePublic(Request $request, int $courseId): JsonResponse
    {
        $perPage = min($request->get('per_page', 50), 100);

        $query = Unit::with(['assessments'])
            ->where('course_id', $courseId)
            ->whereHas('assessments')
            ->orderBy('unit_order');

        $units = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'message' => 'Course units retrieved successfully',
            'data' => $units
        ]);
    }

    /**
     * Clear all assessments from a unit (completely delete assessments)
     */
    public function clearAssessments(int $id): JsonResponse
    {
        try {
            $cleared = $this->service->clearUnitAssessments($id);

            return response()->json([
                'success' => true,
                'message' => "Deleted {$cleared} assessments from unit",
                'data' => ['deleted_count' => $cleared]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear unit assessments',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
