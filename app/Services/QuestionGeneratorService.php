<?php

namespace App\Services;

use App\Models\Unit;
use App\Models\AssessmentMultipleSelect;
use App\Models\Assessment;
use Gemini\Data\Schema;
use Gemini\Enums\DataType;

class QuestionGeneratorService
{
    protected GeminiService $geminiService;

    public function __construct(GeminiService $geminiService)
    {
        $this->geminiService = $geminiService;
    }

    /**
     * Generate multiple choice questions for a unit
     */
    public function generateQuestionsForUnit(Unit $unit, int $count = 3): array
    {
        $prompt = $this->buildQuestionPrompt($unit, $count);
        $schema = $this->getQuestionSchema();

        $result = $this->geminiService->generateStructuredContent($prompt, $schema, [
            'temperature' => 0.8,
            'max_tokens' => 2000,
        ]);

        return $result['questions'] ?? [];
    }

    /**
     * Create and save questions for a unit
     */
    public function createQuestionsForUnit(Unit $unit, int $count = 3): array
    {
        $questions = $this->generateQuestionsForUnit($unit, $count);
        $createdAssessments = [];

        foreach ($questions as $index => $questionData) {
            // Create the AssessmentMultipleSelect record
            $multipleSelect = AssessmentMultipleSelect::create([
                'question' => $questionData['question'],
                'answer_list' => $questionData['options'],
                'correct_answer_indexes' => [$questionData['correct_answer_index']],
                'explanations' => [$questionData['explanation']],
            ]);

            // Create the polymorphic Assessment record
            $assessment = Assessment::create([
                'itemable_type' => AssessmentMultipleSelect::class,
                'itemable_id' => $multipleSelect->id,
            ]);

            // Link the assessment to the unit
            $nextOrder = $unit->assessments()->max('unit_assessments.assessment_order') + 1;
            $unit->assessments()->attach($assessment->id, [
                'assessment_order' => $nextOrder,
            ]);

            $createdAssessments[] = [
                'assessment' => $assessment,
                'multiple_select' => $multipleSelect,
                'question_data' => $questionData,
            ];
        }

        return $createdAssessments;
    }

    /**
     * Generate a single question based on topic and difficulty
     */
    public function generateSingleQuestion(string $topic, string $difficulty, string $skillType): ?array
    {
        $prompt = "Create a single multiple-choice English learning question about '{$topic}'
                   for {$difficulty} level students focusing on {$skillType} skills.

                   The question should be educational, clear, and appropriate for the difficulty level.
                   Provide 4 answer options with only one correct answer.
                   Include a brief explanation for why the correct answer is right.";

        $schema = $this->getSingleQuestionSchema();

        $result = $this->geminiService->generateStructuredContent($prompt, $schema, [
            'temperature' => 0.8,
            'max_tokens' => 800,
        ]);

        return $result;
    }

    /**
     * Build the prompt for generating questions
     */
    protected function buildQuestionPrompt(Unit $unit, int $count): string
    {
        $course = $unit->course;

        return "Create {$count} multiple-choice English learning questions for the following unit:

Course: {$course->title}
Unit: {$unit->title}
Description: {$unit->description}
Skill Type: {$unit->skill_type->value}
Difficulty: {$unit->difficulty->value}

Requirements:
1. Questions should be appropriate for {$unit->difficulty->value} level English learners
2. Focus on {$unit->skill_type->value} skills
3. Each question should have exactly 4 answer options
4. Only one option should be correct
5. Provide clear explanations for the correct answers
6. Questions should be educational and engaging
7. Avoid overly complex grammar or vocabulary unless it's the focus of the question

Generate questions that test understanding of the unit's content and help students learn.";
    }

    /**
     * Get the JSON schema for multiple questions
     */
    protected function getQuestionSchema(): Schema
    {
        return new Schema(
            type: DataType::OBJECT,
            properties: [
                'questions' => new Schema(
                    type: DataType::ARRAY,
                    items: new Schema(
                        type: DataType::OBJECT,
                        properties: [
                            'question' => new Schema(
                                type: DataType::STRING,
                                description: 'The question text'
                            ),
                            'options' => new Schema(
                                type: DataType::ARRAY,
                                items: new Schema(type: DataType::STRING),
                                description: 'Array of 4 answer options'
                            ),
                            'correct_answer_index' => new Schema(
                                type: DataType::INTEGER,
                                description: 'Index of the correct answer (0-3)'
                            ),
                            'explanation' => new Schema(
                                type: DataType::STRING,
                                description: 'Explanation for the correct answer'
                            ),
                        ],
                        required: ['question', 'options', 'correct_answer_index', 'explanation']
                    )
                )
            ],
            required: ['questions']
        );
    }

    /**
     * Get the JSON schema for a single question
     */
    protected function getSingleQuestionSchema(): Schema
    {
        return new Schema(
            type: DataType::OBJECT,
            properties: [
                'question' => new Schema(
                    type: DataType::STRING,
                    description: 'The question text'
                ),
                'options' => new Schema(
                    type: DataType::ARRAY,
                    items: new Schema(type: DataType::STRING),
                    description: 'Array of 4 answer options'
                ),
                'correct_answer_index' => new Schema(
                    type: DataType::INTEGER,
                    description: 'Index of the correct answer (0-3)'
                ),
                'explanation' => new Schema(
                    type: DataType::STRING,
                    description: 'Explanation for the correct answer'
                ),
            ],
            required: ['question', 'options', 'correct_answer_index', 'explanation']
        );
    }
}
