<?php

namespace App\Services;

use App\Models\AssessmentMultipleSelect;

class AssessmentMultipleSelectService extends AssessmentService
{
    /**
     * Create a new AssessmentMultipleSelect with its corresponding Assessment
     */
    public function create(array $data): AssessmentMultipleSelect
    {
        // Create the AssessmentMultipleSelect first
        $assessmentMultipleSelect = AssessmentMultipleSelect::create([
            'question' => $data['question'],
            'answer_list' => $data['answer_list'],
            'correct_answer_indexes' => $data['correct_answer_indexes'],
            'explanations' => $data['explanations'] ?? [],
        ]);

        // Create the polymorphic Assessment record and load relationship
        return $this->createAssessmentRecord($assessmentMultipleSelect);
    }

    /**
     * Update an existing AssessmentMultipleSelect
     */
    public function update(AssessmentMultipleSelect $assessmentMultipleSelect, array $data): AssessmentMultipleSelect
    {
        $assessmentMultipleSelect->update([
            'question' => $data['question'] ?? $assessmentMultipleSelect->question,
            'answer_list' => $data['answer_list'] ?? $assessmentMultipleSelect->answer_list,
            'correct_answer_indexes' => $data['correct_answer_indexes'] ?? $assessmentMultipleSelect->correct_answer_indexes,
            'explanations' => $data['explanations'] ?? $assessmentMultipleSelect->explanations,
        ]);

        return $assessmentMultipleSelect->fresh();
    }
}
