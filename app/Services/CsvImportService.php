<?php

namespace App\Services;

use App\Models\Unit;
use App\Services\UnitService;
use App\Services\AssessmentMultipleSelectService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class CsvImportService
{
    public function __construct(
        private UnitService $unitService,
        private AssessmentMultipleSelectService $assessmentService
    ) {}

    /**
     * Import assessments for a specific unit from CSV file
     */
    public function importAssessmentsForUnit(UploadedFile $file, Unit $unit): array
    {
        // Validate file
        $this->validateFile($file);

        // Parse CSV content for assessments
        $data = $this->parseAssessmentsCsv($file);

        // Validate CSV structure and content for assessments
        $this->validateAssessmentCsvData($data);

        // Process import in transaction
        return DB::transaction(function () use ($data, $unit) {
            return $this->processAssessmentImport($data, $unit);
        });
    }

    /**
     * Validate assessments CSV without importing
     */
    public function validateAssessmentsCsv(UploadedFile $file, Unit $unit): array
    {
        // Validate file
        $this->validateFile($file);

        // Parse CSV content for assessments
        $data = $this->parseAssessmentsCsv($file);

        // Validate CSV structure and content for assessments
        $this->validateAssessmentCsvData($data);

        return [
            'total_assessments' => count($data),
            'preview' => array_slice($data, 0, 3) // Show first 3 rows as preview
        ];
    }

    /**
     * Validate uploaded file
     */
    private function validateFile(UploadedFile $file): void
    {
        $validator = Validator::make(
            ['file' => $file],
            [
                'file' => 'required|file|mimes:csv,txt|max:10240', // 10MB max
            ]
        );

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
    }

    /**
     * Parse CSV file content for assessments only
     */
    private function parseAssessmentsCsv(UploadedFile $file): array
    {
        $path = $file->store('temp', 'local');
        $fullPath = Storage::disk('local')->path($path);

        $data = [];
        $headers = [];

        if (($handle = fopen($fullPath, 'r')) !== FALSE) {
            $rowIndex = 0;

            while (($row = fgetcsv($handle, 1000, ',')) !== FALSE) {
                if ($rowIndex === 0) {
                    // First row is headers
                    $headers = array_map('trim', $row);
                    $this->validateAssessmentHeaders($headers);
                } else {
                    // Data rows
                    if (count(array_filter($row)) > 0) { // Skip empty rows
                        $data[] = array_combine($headers, array_map('trim', $row));
                    }
                }
                $rowIndex++;
            }
            fclose($handle);
        }

        // Clean up temp file
        Storage::disk('local')->delete($path);

        return $data;
    }

    /**
     * Validate assessment-only CSV headers
     */
    private function validateAssessmentHeaders(array $headers): void
    {
        $requiredHeaders = [
            'question',
            'option_1',
            'option_2',
            'correct_answer'
        ];

        $missingHeaders = array_diff($requiredHeaders, $headers);

        if (!empty($missingHeaders)) {
            throw new \InvalidArgumentException(
                'Missing required headers: ' . implode(', ', $missingHeaders)
            );
        }

        // Count option headers to ensure we have at least 2
        $optionHeaders = array_filter($headers, function($header) {
            return preg_match('/^option_\d+$/', $header);
        });

        if (count($optionHeaders) < 2) {
            throw new \InvalidArgumentException(
                'At least 2 option headers (option_1, option_2) are required'
            );
        }
    }

    /**
     * Validate assessment CSV data content
     */
    private function validateAssessmentCsvData(array $data): void
    {
        foreach ($data as $index => $row) {
            $rowNumber = $index + 2; // +2 because of 0-based index and header row

            // Validate required fields
            $this->validateAssessmentRow($row, $rowNumber);
        }
    }

    /**
     * Validate individual assessment row data
     */
    private function validateAssessmentRow(array $row, int $rowNumber): void
    {
        // Build dynamic rules for all option fields
        $rules = [
            'question' => 'required|string|max:1000',
            'correct_answer' => 'required|string',
            'explanation' => 'nullable|string|max:1000'
        ];

        // Add validation rules for all option fields dynamically
        $optionCount = 0;
        foreach ($row as $key => $value) {
            if (preg_match('/^option_(\d+)$/', $key, $matches)) {
                $optionNumber = (int) $matches[1];
                $optionCount = max($optionCount, $optionNumber);

                // First two options are required, rest are optional
                if ($optionNumber <= 2) {
                    $rules[$key] = 'required|string|max:500';
                } else {
                    $rules[$key] = 'nullable|string|max:500';
                }
            }
        }

        // Ensure we have at least 2 options
        if ($optionCount < 2) {
            throw new \InvalidArgumentException(
                "Row {$rowNumber}: At least 2 options (option_1, option_2) are required"
            );
        }

        $validator = Validator::make($row, $rules);

        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            throw new \InvalidArgumentException(
                "Row {$rowNumber}: " . implode(', ', $errors)
            );
        }

        // Collect all option values (filter out empty ones)
        $options = [];
        for ($i = 1; $i <= $optionCount; $i++) {
            $optionKey = "option_{$i}";
            if (isset($row[$optionKey]) && !empty(trim($row[$optionKey]))) {
                $options[] = trim($row[$optionKey]);
            }
        }

        // Validate correct_answer is one of the options
        if (!in_array(trim($row['correct_answer']), $options)) {
            throw new \InvalidArgumentException(
                "Row {$rowNumber}: correct_answer must match one of the provided options"
            );
        }
    }

    /**
     * Process the import and create units/assessments
     */
    private function processImport(array $data, int $courseId): array
    {
        $createdUnits = [];
        $createdAssessments = [];
        $currentUnit = null;
        $currentUnitTitle = null;

        foreach ($data as $row) {
            // Create new unit if title changed or first row
            if ($currentUnitTitle !== $row['unit_title']) {
                $currentUnit = $this->createUnit($row, $courseId);
                $createdUnits[] = $currentUnit;
                $currentUnitTitle = $row['unit_title'];
            }

            // Create assessment for current unit
            $assessment = $this->createAssessment($row, $currentUnit);
            $createdAssessments[] = $assessment;
        }

        return [
            'units_created' => count($createdUnits),
            'assessments_created' => count($createdAssessments),
            'units' => $createdUnits,
            'assessments' => $createdAssessments
        ];
    }

    /**
     * Create unit from row data
     */
    private function createUnit(array $row, int $courseId): Unit
    {
        // Check if unit with same title already exists in this course
        $existingUnit = Unit::where('course_id', $courseId)
            ->where('title', $row['unit_title'])
            ->first();

        if ($existingUnit) {
            return $existingUnit;
        }

        $unitData = [
            'course_id' => $courseId,
            'title' => $row['unit_title'],
            'description' => $row['unit_description'] ?: null,
            'skill_type' => $row['skill_type'],
            'difficulty' => $row['difficulty']
        ];

        return $this->unitService->create($unitData);
    }

    /**
     * Process the assessment import for a specific unit
     */
    private function processAssessmentImport(array $data, Unit $unit): array
    {
        $createdAssessments = [];

        foreach ($data as $row) {
            // Create assessment for the unit
            $assessment = $this->createAssessmentForUnit($row, $unit);
            $createdAssessments[] = $assessment;
        }

        return [
            'assessments_created' => count($createdAssessments),
            'assessments' => $createdAssessments
        ];
    }

    /**
     * Create assessment from row data
     */
    private function createAssessment(array $row, Unit $unit): \App\Models\AssessmentMultipleSelect
    {
        // Dynamically build answer list from all option fields
        $answerList = [];

        // Find all option fields and sort them numerically
        $optionFields = [];
        foreach ($row as $key => $value) {
            if (preg_match('/^option_(\d+)$/', $key, $matches)) {
                $optionFields[(int) $matches[1]] = $value;
            }
        }
        ksort($optionFields);

        // Build answer list (filter out empty options)
        foreach ($optionFields as $option) {
            if (!empty(trim($option))) {
                $answerList[] = trim($option);
            }
        }

        // Find correct answer index
        $correctAnswerIndex = array_search(trim($row['correct_answer']), $answerList);

        $assessmentData = [
            'question' => $row['question'],
            'answer_list' => $answerList,
            'correct_answer_indexes' => [$correctAnswerIndex],
            'explanations' => $row['explanation'] ? [$row['explanation']] : []
        ];

        // Create assessment
        $assessment = $this->assessmentService->create($assessmentData);

        // Attach to unit (get next assessment order)
        $nextOrder = $unit->assessments()->max('assessment_order') + 1;
        $this->assessmentService->attachToUnits($assessment, [
            ['unit_id' => $unit->id, 'assessment_order' => $nextOrder]
        ]);

        return $assessment;
    }

    /**
     * Create assessment for specific unit from row data (assessment-only CSV)
     */
    private function createAssessmentForUnit(array $row, Unit $unit): \App\Models\AssessmentMultipleSelect
    {
        // Dynamically build answer list from all option fields
        $answerList = [];

        // Find all option fields and sort them numerically
        $optionFields = [];
        foreach ($row as $key => $value) {
            if (preg_match('/^option_(\d+)$/', $key, $matches)) {
                $optionFields[(int) $matches[1]] = $value;
            }
        }
        ksort($optionFields);

        // Build answer list (filter out empty options)
        foreach ($optionFields as $option) {
            if (!empty(trim($option))) {
                $answerList[] = trim($option);
            }
        }

        // Find correct answer index
        $correctAnswerIndex = array_search(trim($row['correct_answer']), $answerList);

        $assessmentData = [
            'question' => $row['question'],
            'answer_list' => $answerList,
            'correct_answer_indexes' => [$correctAnswerIndex],
            'explanations' => isset($row['explanation']) && $row['explanation'] ? [$row['explanation']] : []
        ];

        // Create assessment
        $assessment = $this->assessmentService->create($assessmentData);

        // Attach to unit (get next assessment order)
        $nextOrder = $unit->assessments()->max('assessment_order') + 1;
        $this->assessmentService->attachToUnits($assessment, [
            ['unit_id' => $unit->id, 'assessment_order' => $nextOrder]
        ]);

        return $assessment;
    }

    /**
     * Generate CSV template for download (assessment-only format)
     */
    public function generateTemplate(): string
    {
        $headers = [
            'question',
            'option_1',
            'option_2',
            'option_3',
            'option_4',
            'option_5',
            'option_6',
            'correct_answer',
            'explanation'
        ];

        $sampleData = [
            [
                'What is the English word for "hello"?',
                'Hello',
                'Goodbye',
                'Please',
                'Thank you',
                'Sorry',
                'Welcome',
                'Hello',
                'Hello is a common greeting used when meeting someone'
            ],
            [
                'How do you say "goodbye" in English?',
                'Hello',
                'Goodbye',
                'Please',
                'Sorry',
                '',
                '',
                'Goodbye',
                'Goodbye is used when leaving or parting from someone'
            ],
            [
                'Which word means "please"?',
                'Hello',
                'Goodbye',
                'Please',
                '',
                '',
                '',
                'Please',
                'Please is used to make polite requests'
            ]
        ];

        $csvContent = '';

        // Add headers
        $csvContent .= implode(',', array_map(function($header) {
            return '"' . str_replace('"', '""', $header) . '"';
        }, $headers)) . "\n";

        // Add sample data
        foreach ($sampleData as $row) {
            $csvContent .= implode(',', array_map(function($field) {
                return '"' . str_replace('"', '""', $field) . '"';
            }, $row)) . "\n";
        }

        return $csvContent;
    }
}
