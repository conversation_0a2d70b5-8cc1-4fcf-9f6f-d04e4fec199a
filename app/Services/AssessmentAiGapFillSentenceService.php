<?php

namespace App\Services;

use App\Models\AssessmentAiGapFillSentence;

class AssessmentAiGapFillSentenceService extends AssessmentService
{
    /**
     * Create a new AssessmentAiGapFillSentence with its corresponding Assessment
     */
    public function create(array $data): AssessmentAiGapFillSentence
    {
        // Create the AssessmentAiGapFillSentence first
        // fill_position will be auto-calculated in the model's boot method
        $assessmentAiGapFillSentence = AssessmentAiGapFillSentence::create([
            'question' => $data['question'],
            'context' => $data['context'],
        ]);

        // Create the polymorphic Assessment record and return with relationship loaded
        return $this->createAssessmentRecord($assessmentAiGapFillSentence);
    }

    /**
     * Update an existing AssessmentAiGapFillSentence
     */
    public function update(AssessmentAiGapFillSentence $assessmentAiGapFillSentence, array $data): AssessmentAiGapFillSentence
    {
        // fill_position will be auto-calculated in the model's boot method if question changes
        $assessmentAiGapFillSentence->update([
            'question' => $data['question'] ?? $assessmentAiGapFillSentence->question,
            'context' => $data['context'] ?? $assessmentAiGapFillSentence->context,
        ]);

        return $assessmentAiGapFillSentence->fresh();
    }
}