<?php

namespace App\Services;

use App\Models\Course;
use App\Models\Unit;
use App\Models\AssessmentMultipleSelect;
use Gemini\Data\Schema;
use Gemini\Enums\DataType;

class ContentEnhancerService
{
    protected GeminiService $geminiService;

    public function __construct(GeminiService $geminiService)
    {
        $this->geminiService = $geminiService;
    }

    /**
     * Enhance a course description
     */
    public function enhanceCourseDescription(Course $course): ?string
    {
        $prompt = "Improve the following English learning course description to make it more engaging,
                   informative, and appealing to students. Keep it concise but compelling.

                   Current title: {$course->title}
                   Current description: {$course->description}

                   Provide an enhanced description that:
                   1. Clearly explains what students will learn
                   2. Highlights the benefits and outcomes
                   3. Uses engaging and motivational language
                   4. Remains professional and educational
                   5. Is approximately 100-200 words";

        return $this->geminiService->generateContent($prompt, [
            'temperature' => 0.7,
            'max_tokens' => 500,
        ]);
    }

    /**
     * Enhance a unit description
     */
    public function enhanceUnitDescription(Unit $unit): ?string
    {
        $course = $unit->course;

        $prompt = "Improve the following English learning unit description to make it more detailed,
                   engaging, and educational.

                   Course: {$course->title}
                   Unit title: {$unit->title}
                   Current description: {$unit->description}
                   Skill type: {$unit->skill_type->value}
                   Difficulty: {$unit->difficulty->value}

                   Provide an enhanced description that:
                   1. Clearly explains the learning objectives
                   2. Describes what specific skills will be developed
                   3. Mentions practical applications
                   4. Is appropriate for {$unit->difficulty->value} level learners
                   5. Focuses on {$unit->skill_type->value} skills
                   6. Is approximately 50-150 words";

        return $this->geminiService->generateContent($prompt, [
            'temperature' => 0.7,
            'max_tokens' => 400,
        ]);
    }

    /**
     * Improve an assessment question
     */
    public function enhanceAssessmentQuestion(AssessmentMultipleSelect $assessment): ?array
    {
        $prompt = "Improve the following English learning multiple-choice question to make it clearer,
                   more educational, and better structured.

                   Current question: {$assessment->question}
                   Current options: " . implode(', ', $assessment->answer_list) . "
                   Current correct answer: " . ($assessment->answer_list[$assessment->correct_answer_indexes[0]] ?? 'Unknown') . "
                   Current explanation: " . ($assessment->explanations[0] ?? 'No explanation') . "

                   Provide an improved version that:
                   1. Has a clear, well-structured question
                   2. Includes 4 distinct and plausible answer options
                   3. Has only one clearly correct answer
                   4. Provides a helpful explanation
                   5. Is educational and appropriate for English learners";

        $schema = new Schema(
            type: DataType::OBJECT,
            properties: [
                'question' => new Schema(
                    type: DataType::STRING,
                    description: 'The improved question text'
                ),
                'options' => new Schema(
                    type: DataType::ARRAY,
                    items: new Schema(type: DataType::STRING),
                    description: 'Array of 4 improved answer options'
                ),
                'correct_answer_index' => new Schema(
                    type: DataType::INTEGER,
                    description: 'Index of the correct answer (0-3)'
                ),
                'explanation' => new Schema(
                    type: DataType::STRING,
                    description: 'Improved explanation for the correct answer'
                ),
            ],
            required: ['question', 'options', 'correct_answer_index', 'explanation']
        );

        return $this->geminiService->generateStructuredContent($prompt, $schema, [
            'temperature' => 0.6,
            'max_tokens' => 800,
        ]);
    }

    /**
     * Suggest difficulty level for content
     */
    public function suggestDifficultyLevel(string $content, string $contentType = 'text'): ?string
    {
        $prompt = "Analyze the following English learning {$contentType} and suggest the most appropriate
                   difficulty level from: beginner, elementary, intermediate, upper_intermediate, advanced, proficient.

                   Content: {$content}

                   Consider:
                   1. Vocabulary complexity
                   2. Grammar structures used
                   3. Sentence length and complexity
                   4. Conceptual difficulty
                   5. Required prior knowledge

                   Respond with only the difficulty level (one word).";

        return $this->geminiService->generateContent($prompt, [
            'temperature' => 0.3,
            'max_tokens' => 50,
        ]);
    }

    /**
     * Generate learning objectives for a unit
     */
    public function generateLearningObjectives(Unit $unit): ?array
    {
        $course = $unit->course;

        $prompt = "Generate 3-5 clear learning objectives for the following English learning unit:

                   Course: {$course->title}
                   Unit: {$unit->title}
                   Description: {$unit->description}
                   Skill type: {$unit->skill_type->value}
                   Difficulty: {$unit->difficulty->value}

                   Learning objectives should:
                   1. Start with action verbs (e.g., identify, explain, demonstrate, apply)
                   2. Be specific and measurable
                   3. Be appropriate for {$unit->difficulty->value} level learners
                   4. Focus on {$unit->skill_type->value} skills
                   5. Be achievable within the unit scope";

        $schema = new Schema(
            type: DataType::OBJECT,
            properties: [
                'objectives' => new Schema(
                    type: DataType::ARRAY,
                    items: new Schema(type: DataType::STRING),
                    description: 'Array of learning objectives'
                )
            ],
            required: ['objectives']
        );

        $result = $this->geminiService->generateStructuredContent($prompt, $schema, [
            'temperature' => 0.7,
            'max_tokens' => 600,
        ]);

        return $result['objectives'] ?? null;
    }

    /**
     * Translate content to different languages
     */
    public function translateContent(string $content, string $targetLanguage): ?string
    {
        $prompt = "Translate the following English learning content to {$targetLanguage}.
                   Maintain the educational context and ensure the translation is accurate and natural.

                   Content: {$content}";

        return $this->geminiService->generateContent($prompt, [
            'temperature' => 0.3,
            'max_tokens' => 1000,
        ]);
    }

    /**
     * Generate study tips for a unit
     */
    public function generateStudyTips(Unit $unit): ?array
    {
        $prompt = "Generate 3-5 practical study tips for students learning this English unit:

                   Unit: {$unit->title}
                   Description: {$unit->description}
                   Skill type: {$unit->skill_type->value}
                   Difficulty: {$unit->difficulty->value}

                   Tips should be:
                   1. Specific to the skill type and difficulty level
                   2. Actionable and practical
                   3. Helpful for self-study
                   4. Encouraging and motivational";

        $schema = new Schema(
            type: DataType::OBJECT,
            properties: [
                'tips' => new Schema(
                    type: DataType::ARRAY,
                    items: new Schema(type: DataType::STRING),
                    description: 'Array of study tips'
                )
            ],
            required: ['tips']
        );

        $result = $this->geminiService->generateStructuredContent($prompt, $schema, [
            'temperature' => 0.8,
            'max_tokens' => 800,
        ]);

        return $result['tips'] ?? null;
    }
}
