<?php

namespace App\Services;

use App\Models\Course;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class CourseService
{
    /**
     * Create a new course
     */
    public function create(array $data): Course
    {
        return DB::transaction(function () use ($data) {
            return Course::create($data);
        });
    }

    /**
     * Update an existing course
     */
    public function update(Course $course, array $data): Course
    {
        return DB::transaction(function () use ($course, $data) {
            $course->update($data);
            return $course->fresh();
        });
    }

    /**
     * Delete a course and handle related units
     */
    public function delete(Course $course): bool
    {
        return DB::transaction(function () use ($course) {
            // Check if course has units
            if ($course->units()->count() > 0) {
                throw new \Exception('Cannot delete course that contains units. Please remove or move units first.');
            }

            return $course->delete();
        });
    }

    /**
     * Duplicate a course with all its units and assessments
     */
    public function duplicate(Course $course): Course
    {
        return DB::transaction(function () use ($course) {
            // Create new course with duplicated title
            $newCourse = Course::create([
                'title' => $this->generateUniqueTitle($course->title),
                'description' => $course->description,
            ]);

            // Load units with their assessments
            $units = $course->units()->with('assessments')->get();

            foreach ($units as $unit) {
                // Create new unit
                $newUnit = $newCourse->units()->create([
                    'title' => $unit->title,
                    'description' => $unit->description,
                    'skill_type' => $unit->skill_type,
                    'difficulty' => $unit->difficulty,
                    'unit_order' => $unit->unit_order,
                ]);

                // Duplicate assessments if any
                if ($unit->assessments->isNotEmpty()) {
                    $assessmentIds = $unit->assessments->pluck('id')->toArray();
                    $assessmentData = [];
                    
                    foreach ($unit->assessments as $assessment) {
                        $assessmentData[$assessment->id] = [
                            'assessment_order' => $assessment->pivot->assessment_order
                        ];
                    }

                    $newUnit->assessments()->attach($assessmentData);
                }
            }

            return $newCourse->load(['units.assessments']);
        });
    }

    /**
     * Generate a unique title for duplicated course
     */
    private function generateUniqueTitle(string $originalTitle): string
    {
        $baseTitle = $originalTitle;
        $counter = 1;

        // Check if title already has a copy suffix
        if (preg_match('/^(.+) \(Copy( \d+)?\)$/', $originalTitle, $matches)) {
            $baseTitle = $matches[1];
            $counter = isset($matches[2]) ? (int)trim($matches[2]) + 1 : 2;
        }

        do {
            $newTitle = $counter === 1 
                ? "{$baseTitle} (Copy)"
                : "{$baseTitle} (Copy {$counter})";
            
            $exists = Course::where('title', $newTitle)->exists();
            $counter++;
        } while ($exists);

        return $newTitle;
    }

    /**
     * Get courses with statistical information
     */
    public function getCoursesWithStats(): Collection
    {
        return Course::withCount([
            'units',
            'units as total_assessments' => function ($query) {
                $query->join('unit_assessments', 'units.id', '=', 'unit_assessments.unit_id');
            }
        ])
        ->with(['units' => function ($query) {
            $query->select('id', 'course_id', 'title', 'skill_type', 'difficulty')
                  ->orderBy('unit_order');
        }])
        ->orderBy('created_at', 'desc')
        ->get();
    }

    /**
     * Force delete a course and all its related data
     * WARNING: This will permanently delete all units and assessments
     */
    public function forceDelete(Course $course): bool
    {
        return DB::transaction(function () use ($course) {
            // Get all units for this course
            $units = $course->units()->get();

            foreach ($units as $unit) {
                // Detach all assessments from unit
                $unit->assessments()->detach();
                
                // Delete the unit
                $unit->delete();
            }

            // Finally delete the course
            return $course->delete();
        });
    }

    /**
     * Move all units from one course to another
     */
    public function moveAllUnits(Course $sourceCourse, Course $targetCourse): Course
    {
        return DB::transaction(function () use ($sourceCourse, $targetCourse) {
            // Get the highest unit order in target course
            $maxOrder = $targetCourse->units()->max('unit_order') ?? 0;

            // Update all units from source course
            $sourceCourse->units()->update([
                'course_id' => $targetCourse->id,
                'unit_order' => DB::raw("unit_order + {$maxOrder}")
            ]);

            return $targetCourse->fresh(['units.assessments']);
        });
    }
}