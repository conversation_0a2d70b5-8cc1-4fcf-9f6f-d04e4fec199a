<?php

namespace App\Services;

use Gemini\Data\GenerationConfig;
use Gemini\Data\Schema;
use Gemini\Enums\DataType;
use Gemini\Enums\ResponseMimeType;
use Gemini\Laravel\Facades\Gemini;
use Illuminate\Support\Facades\Log;

class GeminiService
{
    /**
     * Generate content using Gemini AI
     */
    public function generateContent(string $prompt, array $options = []): ?string
    {
        try {
            $model = $options['model'] ?? 'gemini-2.0-flash';
            $temperature = $options['temperature'] ?? 0.7;
            $maxTokens = $options['max_tokens'] ?? 1000;

            $generationConfig = new GenerationConfig(
                temperature: $temperature,
                maxOutputTokens: $maxTokens,
            );

            $result = Gemini::generativeModel(model: $model)
                ->withGenerationConfig($generationConfig)
                ->generateContent($prompt);

            return $result->text();
        } catch (\Exception $e) {
            Log::error('Gemini API Error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Generate structured JSON content
     */
    public function generateStructuredContent(string $prompt, Schema $schema, array $options = []): ?array
    {
        try {
            $model = $options['model'] ?? 'gemini-2.0-flash';
            $temperature = $options['temperature'] ?? 0.7;
            $maxTokens = $options['max_tokens'] ?? 1000;

            $generationConfig = new GenerationConfig(
                responseMimeType: ResponseMimeType::APPLICATION_JSON,
                responseSchema: $schema,
                temperature: $temperature,
                maxOutputTokens: $maxTokens,
            );

            $result = Gemini::generativeModel(model: $model)
                ->withGenerationConfig($generationConfig)
                ->generateContent($prompt);

            $jsonResult = $result->json();
            return is_array($jsonResult) ? $jsonResult : (array) $jsonResult;
        } catch (\Exception $e) {
            Log::error('Gemini Structured API Error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Count tokens in a text
     */
    public function countTokens(string $text, string $model = 'gemini-2.0-flash'): int
    {
        try {
            $response = Gemini::generativeModel(model: $model)
                ->countTokens($text);

            return $response->totalTokens;
        } catch (\Exception $e) {
            Log::error('Gemini Token Count Error: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Generate embeddings for text
     */
    public function generateEmbedding(string $text): ?array
    {
        try {
            $response = Gemini::embeddingModel('text-embedding-004')
                ->embedContent($text);

            return $response->embedding->values;
        } catch (\Exception $e) {
            Log::error('Gemini Embedding Error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Start a chat session
     */
    public function startChat(array $history = [], array $options = [])
    {
        try {
            $model = $options['model'] ?? 'gemini-2.0-flash';
            $temperature = $options['temperature'] ?? 0.7;
            $maxTokens = $options['max_tokens'] ?? 1000;

            $generationConfig = new GenerationConfig(
                temperature: $temperature,
                maxOutputTokens: $maxTokens,
            );

            return Gemini::generativeModel(model: $model)
                ->withGenerationConfig($generationConfig)
                ->startChat(history: $history);
        } catch (\Exception $e) {
            Log::error('Gemini Chat Error: ' . $e->getMessage());
            return null;
        }
    }
}
