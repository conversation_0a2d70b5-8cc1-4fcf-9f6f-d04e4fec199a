<?php

namespace App\Services;

use App\Models\Unit;
use App\Models\Course;
use App\Models\Assessment;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UnitService
{
    /**
     * Create a new Unit
     */
    public function create(array $data): Unit
    {
        // Validate course exists
        $course = Course::findOrFail($data['course_id']);
        
        // Auto-set unit_order if not provided
        if (!isset($data['unit_order'])) {
            $data['unit_order'] = $course->units()->max('unit_order') + 1;
        }

        return Unit::create($data);
    }

    /**
     * Update an existing Unit
     */
    public function update(Unit $unit, array $data): Unit
    {
        // If course_id is being changed, validate new course exists
        if (isset($data['course_id']) && $data['course_id'] !== $unit->course_id) {
            Course::findOrFail($data['course_id']);
        }

        // If unit_order is being changed within same course, handle reordering
        if (isset($data['unit_order']) && $data['unit_order'] !== $unit->unit_order) {
            $this->reorderUnits($unit, $data['unit_order'], $data['course_id'] ?? $unit->course_id);
        }

        $unit->update($data);

        return $unit->fresh();
    }

    /**
     * Delete a Unit and reorder remaining units
     */
    public function delete(Unit $unit): bool
    {
        $courseId = $unit->course_id;
        $deletedOrder = $unit->unit_order;
        
        $deleted = $unit->delete();
        
        if ($deleted) {
            // Reorder remaining units to fill the gap
            Unit::where('course_id', $courseId)
                ->where('unit_order', '>', $deletedOrder)
                ->decrement('unit_order');
        }

        return $deleted;
    }

    /**
     * Reorder units within a course
     */
    public function reorderUnits(Unit $unit, int $newOrder, int $courseId): void
    {
        $oldOrder = $unit->unit_order;
        
        if ($oldOrder === $newOrder) {
            return;
        }

        // Moving up (decreasing order number)
        if ($newOrder < $oldOrder) {
            Unit::where('course_id', $courseId)
                ->whereBetween('unit_order', [$newOrder, $oldOrder - 1])
                ->increment('unit_order');
        }
        // Moving down (increasing order number)  
        else {
            Unit::where('course_id', $courseId)
                ->whereBetween('unit_order', [$oldOrder + 1, $newOrder])
                ->decrement('unit_order');
        }
    }

    /**
     * Duplicate a unit (copy with new order)
     */
    public function duplicate(Unit $unit, ?int $targetOrder = null): Unit
    {
        $newOrder = $targetOrder ?? ($unit->course->units()->max('unit_order') + 1);
        
        $duplicatedUnit = Unit::create([
            'course_id' => $unit->course_id,
            'title' => $unit->title . ' (Copy)',
            'description' => $unit->description,
            'skill_type' => $unit->skill_type,
            'difficulty' => $unit->difficulty,
            'unit_order' => $newOrder,
        ]);

        // Copy assessment relationships
        foreach ($unit->assessments as $assessment) {
            $duplicatedUnit->assessments()->attach($assessment->id, [
                'assessment_order' => $assessment->pivot->assessment_order
            ]);
        }

        return $duplicatedUnit;
    }

    /**
     * Move unit to different course
     */
    public function moveToCourse(Unit $unit, int $targetCourseId, ?int $targetOrder = null): Unit
    {
        $targetCourse = Course::findOrFail($targetCourseId);
        $oldCourseId = $unit->course_id;
        $oldOrder = $unit->unit_order;
        
        $newOrder = $targetOrder ?? ($targetCourse->units()->max('unit_order') + 1);
        
        // Update unit with new course and order
        $unit->update([
            'course_id' => $targetCourseId,
            'unit_order' => $newOrder
        ]);
        
        // Reorder units in old course to fill the gap
        Unit::where('course_id', $oldCourseId)
            ->where('unit_order', '>', $oldOrder)
            ->decrement('unit_order');
            
        return $unit->fresh();
    }

    /**
     * Clear all assessments from a unit (delete Assessment and AssessmentMultipleSelect records completely)
     */
    public function clearUnitAssessments(int $unitId): int
    {
        try {
            DB::beginTransaction();

            // Verify unit exists and load assessments
            $unit = Unit::with('assessments.itemable')->findOrFail($unitId);

            $assessments = $unit->assessments;
            
            if ($assessments->isEmpty()) {
                DB::commit();
                return 0;
            }

            $clearedCount = $assessments->count();
            Log::info("Starting to clear {$clearedCount} assessments from unit {$unitId}");

            // Delete Assessment records and their itemable records using models
            foreach ($assessments as $assessment) {
                // Delete the concrete assessment record using polymorphic relationship
                if ($assessment->itemable) {
                    $itemableType = class_basename($assessment->itemable);
                    $itemableId = $assessment->itemable->id;
                    $assessment->itemable->delete();
                    Log::debug("Deleted {$itemableType} ID: {$itemableId}");
                }

                // Detach from all units (removes from pivot table)
                $assessment->units()->detach();
                
                // Delete the base Assessment record using model
                $assessment->delete();
                Log::debug("Deleted Assessment ID: {$assessment->id}");
            }

            Log::info("Successfully cleared and deleted {$clearedCount} assessments from unit {$unitId}");
            DB::commit();
            return $clearedCount;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to clear assessments from unit {$unitId}: " . $e->getMessage());
            throw $e;
        }
    }
}