<?php

namespace App\Models;

use App\Enums\Difficulty;
use App\Enums\SkillType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Unit extends Model
{
    use HasFactory;

    protected $fillable = [
        'course_id',
        'title',
        'description',
        'skill_type',
        'difficulty',
        'unit_type',
        'unit_order',
    ];

    protected $casts = [
        'skill_type' => SkillType::class,
        'difficulty' => Difficulty::class,
        'unit_type' => \App\Enums\UnitType::class,
        'unit_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    public function assessments(): BelongsToMany
    {
        return $this->belongsToMany(Assessment::class, 'unit_assessments')
            ->withPivot('assessment_order')
            ->orderByPivot('assessment_order')
            ->with('itemable');
    }

    /**
     * Get assessments with direct itemable content
     */
    public function getDirectAssessments()
    {
        return $this->assessments->map(function ($assessment) {
            if ($assessment->itemable) {
                // Add assessment_order to the itemable object
                $itemable = $assessment->itemable;
                $itemable->assessment_order = $assessment->pivot->assessment_order;
                return $itemable;
            }
            return $assessment;
        });
    }

    /**
     * Get multiple select assessments directly with proper ordering
     */
    public function directAssessments(): BelongsToMany
    {
        return $this->hasManyThrough(
            AssessmentMultipleSelect::class,
            Assessment::class,
            'unit_assessments.unit_id', // Foreign key on unit_assessments table
            'id', // Foreign key on assessment_multiple_selects table
            'id', // Local key on units table
            'itemable_id' // Local key on assessments table
        )
         ->orderBy('unit_assessments.assessment_order');
    }

    /**
     * Check if this unit is for AI Gap Fill Sentence assessments
     */
    public function isAiGapFillSentenceUnit(): bool
    {
        return $this->unit_type === \App\Enums\UnitType::AI_GAP_FILL_SENTENCE;
    }

    /**
     * Check if this unit is for Multiple Select assessments
     */
    public function isMultipleSelectUnit(): bool
    {
        return $this->unit_type === \App\Enums\UnitType::MULTIPLE_SELECT;
    }

    /**
     * Get assessments filtered by unit type
     */
    public function getTypeSpecificAssessments()
    {
        return $this->assessments->filter(function ($assessment) {
            $expectedClass = \App\Enums\UnitType::getAssessmentModelClass($this->unit_type);
            return $assessment->itemable_type === $expectedClass;
        });
    }

    /**
     * Scope to filter units by type
     */
    public function scopeOfType($query, $unitType)
    {
        return $query->where('unit_type', $unitType);
    }

    /**
     * Scope to get AI Gap Fill Sentence units only
     */
    public function scopeAiGapFillSentence($query)
    {
        return $query->where('unit_type', \App\Enums\UnitType::AI_GAP_FILL_SENTENCE);
    }

    /**
     * Scope to get Multiple Select units only
     */
    public function scopeMultipleSelect($query)
    {
        return $query->where('unit_type', \App\Enums\UnitType::MULTIPLE_SELECT);
    }
}
