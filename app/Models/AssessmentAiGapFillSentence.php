<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphOne;

class AssessmentAiGapFillSentence extends Model
{
    use HasFactory;

    protected $fillable = [
        'question',
        'context',
    ];

    protected $casts = [
        'fill_position' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function assessment(): MorphOne
    {
        return $this->morphOne(Assessment::class, 'itemable');
    }

    /**
     * Boot the model to auto-calculate fill_position from question
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->fill_position = $model->calculateFillPositions($model->question);
        });

        static::updating(function ($model) {
            if ($model->isDirty('question')) {
                $model->fill_position = $model->calculateFillPositions($model->question);
            }
        });
    }

    /**
     * Calculate fill positions from question string
     * Finds all occurrences of underscore sequences (____) and returns their start positions
     * Supports multiple gaps: "I ____ to the store and bought _____ apples" -> [2, 35]
     */
    public function calculateFillPositions(string $question): array
    {
        $positions = [];
        $pattern = '/_{1,}/'; // Match one or more underscores
        $offset = 0;

        while (preg_match($pattern, $question, $matches, PREG_OFFSET_CAPTURE, $offset)) {
            $positions[] = $matches[0][1]; // Store the start position (index)
            $offset = $matches[0][1] + strlen($matches[0][0]); // Move past this match
        }

        return $positions;
    }

    /**
     * Get the sentence with gap markers for display
     * Converts the original question with underscores to context with numbered gaps
     * Example: "I ____ to store" -> "I [GAP_1] to store"
     */
    public function getSentenceWithGapsAttribute(): string
    {
        $sentence = $this->question;
        $positions = $this->fill_position ?? [];
        
        if (empty($positions)) {
            return $sentence;
        }
        
        // Sort positions in reverse order to maintain correct indices when replacing
        rsort($positions);
        $gapNumber = count($positions);
        
        foreach ($positions as $position) {
            // Find the underscore sequence at this position
            $pattern = '/_{1,}/';
            if (preg_match($pattern, substr($sentence, $position), $matches)) {
                $underscoreLength = strlen($matches[0]);
                $gapMarker = "[GAP_{$gapNumber}]";
                $sentence = substr_replace($sentence, $gapMarker, $position, $underscoreLength);
                $gapNumber--;
            }
        }
        
        return $sentence;
    }

    /**
     * Check if a position is a fill position
     */
    public function isFillPosition(int $position): bool
    {
        return in_array($position, $this->fill_position ?? []);
    }

    /**
     * Get the number of gaps in this assessment
     */
    public function getGapCount(): int
    {
        return count($this->fill_position ?? []);
    }

    /**
     * Get gap information with positions and lengths
     * Returns array with gap details for each underscore sequence
     */
    public function getGapDetails(): array
    {
        $question = $this->question;
        $gaps = [];
        $pattern = '/_{1,}/';
        $offset = 0;
        $gapNumber = 1;

        while (preg_match($pattern, $question, $matches, PREG_OFFSET_CAPTURE, $offset)) {
            $startPosition = $matches[0][1];
            $length = strlen($matches[0][0]);
            
            $gaps[] = [
                'gap_number' => $gapNumber,
                'start_position' => $startPosition,
                'length' => $length,
                'underscore_sequence' => $matches[0][0],
                'end_position' => $startPosition + $length - 1
            ];
            
            $offset = $startPosition + $length;
            $gapNumber++;
        }

        return $gaps;
    }

    /**
     * Get the question with numbered gap placeholders
     * Example: "I ____ to store and _____ there" -> "I [1] to store and [2] there"
     */
    public function getQuestionWithNumberedGaps(): string
    {
        $question = $this->question;
        $gapDetails = $this->getGapDetails();
        
        // Process gaps in reverse order to maintain correct positions
        $gapDetails = array_reverse($gapDetails);
        
        foreach ($gapDetails as $gap) {
            $placeholder = "[{$gap['gap_number']}]";
            $question = substr_replace(
                $question, 
                $placeholder, 
                $gap['start_position'], 
                $gap['length']
            );
        }
        
        return $question;
    }

    /**
     * Validate that the question contains valid gap markers
     */
    public function hasValidGaps(): bool
    {
        return preg_match('/_{1,}/', $this->question) > 0;
    }
}