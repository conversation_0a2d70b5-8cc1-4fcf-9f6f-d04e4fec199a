<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\Pivot;

class UnitAssessment extends Pivot
{
    protected $table = 'unit_assessments';

    protected $fillable = [
        'unit_id',
        'assessment_id',
        'assessment_order',
    ];

    protected $casts = [
        'unit_id' => 'integer',
        'assessment_id' => 'integer',
        'assessment_order' => 'integer',
    ];

    public $timestamps = false;
}