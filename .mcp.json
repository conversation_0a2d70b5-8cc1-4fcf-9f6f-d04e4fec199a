{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "Context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"]}, "serena": {"type": "stdio", "command": "uv", "args": ["run", "--directory", "/home/<USER>/self/serena", "serena-mcp-server", "--context", "ide-assistant", "--project", "/home/<USER>/english_game_backend"], "env": {}}}}