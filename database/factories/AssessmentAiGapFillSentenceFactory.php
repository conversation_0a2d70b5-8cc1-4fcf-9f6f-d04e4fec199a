<?php

namespace Database\Factories;

use App\Models\AssessmentAiGapFillSentence;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AssessmentAiGapFillSentence>
 */
class AssessmentAiGapFillSentenceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = AssessmentAiGapFillSentence::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $questionTemplates = [
            'The cat ____ on the mat.',
            'I ____ breakfast every morning.',
            'She _____ to school by bus.',
            'Yesterday I ____ to the park and _____ my friends.',
            'The weather is _____ today, so we can _____ outside.',
            'My brother _____ football every ______ after school.',
            'If I _____ enough money, I _____ buy a new car.',
            'The scientist _______ that the experiment _____ successful.',
            'Although it was ______, they _______ to complete the project.',
            'The company\'s _______ strategy has _______ significantly.',
            'Having _______ the presentation, she _______ confident.',
            'The _______ implications of this policy _______ far-reaching.',
            'Were it not for the _______ intervention, the situation _______ deteriorated.',
            'The author _______ that modern technology has _______ communication.',
            'According to the passage, the _______ causes _______ human activities.',
            'In conclusion, the _______ evidence _______ that research is necessary.',
            '_______, I would like to _______ my sincere gratitude.',
        ];

        $contextTemplates = [
            'A simple sentence about daily activities.',
            'A description of routine behaviors.',
            'Past events and social interactions.',
            'Weather conditions and outdoor activities.',
            'Sports and regular schedules.',
            'Conditional statements and possibilities.',
            'Scientific research and conclusions.',
            'Business development and strategy.',
            'Professional preparation and confidence.',
            'Policy analysis and broader implications.',
            'Hypothetical scenarios and consequences.',
            'Literary analysis of modern topics.',
            'Scientific texts about current issues.',
            'Academic writing patterns.',
            'Formal communication expressions.',
        ];

        return [
            'question' => $this->faker->randomElement($questionTemplates),
            'context' => $this->faker->randomElement($contextTemplates),
        ];
    }

    /**
     * Create a beginner level assessment
     */
    public function beginner(): static
    {
        return $this->state(fn (array $attributes) => [
            'question' => $this->faker->randomElement([
                'The cat ____ on the mat.',
                'I ____ breakfast every morning.',
                'She _____ to school by bus.',
            ]),
            'context' => 'A simple sentence for beginner English learners.',
        ]);
    }

    /**
     * Create an elementary level assessment
     */
    public function elementary(): static
    {
        return $this->state(fn (array $attributes) => [
            'question' => $this->faker->randomElement([
                'Yesterday I ____ to the park and _____ my friends.',
                'The weather is _____ today, so we can _____ outside.',
                'My brother _____ football every ______ after school.',
            ]),
            'context' => 'An elementary level sentence with multiple gaps.',
        ]);
    }

    /**
     * Create an intermediate level assessment
     */
    public function intermediate(): static
    {
        return $this->state(fn (array $attributes) => [
            'question' => $this->faker->randomElement([
                'If I _____ enough money, I _____ buy a new car.',
                'The scientist _______ that the experiment _____ successful.',
                'Although it was ______, they _______ to complete the project.',
            ]),
            'context' => 'An intermediate level sentence with complex grammar.',
        ]);
    }

    /**
     * Create an advanced level assessment
     */
    public function advanced(): static
    {
        return $this->state(fn (array $attributes) => [
            'question' => $this->faker->randomElement([
                'The _______ implications of this policy _______ far-reaching.',
                'Were it not for the _______ intervention, the situation _______ deteriorated.',
                'Having _______ the presentation, she _______ confident about the outcome.',
            ]),
            'context' => 'An advanced level sentence with sophisticated vocabulary.',
        ]);
    }

    /**
     * Create assessment with exactly two gaps
     */
    public function twoGaps(): static
    {
        return $this->state(fn (array $attributes) => [
            'question' => $this->faker->randomElement([
                'Yesterday I ____ to the park and _____ my friends.',
                'The weather is _____ today, so we can _____ outside.',
                'If I _____ enough money, I _____ buy a new car.',
                'The scientist _______ that the experiment _____ successful.',
                'Although it was ______, they _______ to complete the project.',
                'The company\'s _______ strategy has _______ significantly.',
                'Having _______ the presentation, she _______ confident.',
            ]),
            'context' => 'A sentence with exactly two gaps to fill.',
        ]);
    }

    /**
     * Create assessment with three or more gaps
     */
    public function multipleGaps(): static
    {
        return $this->state(fn (array $attributes) => [
            'question' => $this->faker->randomElement([
                'The _______ implications of this _______ policy _______ far-reaching.',
                'Were it not for the _______ intervention, the _______ situation _______ deteriorated.',
                'The author _______ that modern _______ technology has _______ communication.',
                'According to the _______ passage, the _______ causes _______ human activities.',
                'In _______, the _______ evidence _______ that further research is _______.',
                'The _______ manager _______ the proposal after _______ consideration.',
                'Students should _______ their homework _______ before _______ to class.',
            ]),
            'context' => 'A complex sentence with multiple gaps to test advanced skills.',
        ]);
    }

    /**
     * Create assessment with varied gap lengths
     */
    public function variedGapLengths(): static
    {
        return $this->state(fn (array $attributes) => [
            'question' => $this->faker->randomElement([
                'I _ to the store and bought _____ apples.',
                'The cat __ on the mat and _______ quietly.',
                'She _____ very _________ about the exam results.',
                'We _ planning to _____ our vacation ______.',
                'The dog __ playing in the _______ with other _______.',
            ]),
            'context' => 'A sentence with gaps of different lengths.',
        ]);
    }

    /**
     * Create assessment with single gap
     */
    public function singleGap(): static
    {
        return $this->state(fn (array $attributes) => [
            'question' => $this->faker->randomElement([
                'The cat ____ on the mat.',
                'I ____ breakfast every morning.',
                'She _____ to school by bus.',
                'The weather is _____ today.',
            ]),
            'context' => 'A sentence with a single gap to fill.',
        ]);
    }

    /**
     * Create assessment with multiple gaps
     */
    public function multipleGaps(): static
    {
        return $this->state(fn (array $attributes) => [
            'question' => $this->faker->randomElement([
                'Yesterday I ____ to the park and _____ my friends.',
                'The weather is _____ today, so we can _____ outside.',
                'If I _____ enough money, I _____ buy a new car.',
                'The _______ implications of this policy _______ far-reaching.',
            ]),
            'context' => 'A sentence with multiple gaps to fill.',
        ]);
    }
}