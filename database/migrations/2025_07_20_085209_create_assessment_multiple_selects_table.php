<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assessment_multiple_selects', function (Blueprint $table) {
            $table->id();
            $table->text('question');
            $table->json('answer_list');
            $table->json('correct_answer_indexes');
            $table->json('explanations');
            $table->boolean('shuffle_options')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assessment_multiple_selects');
    }
};
