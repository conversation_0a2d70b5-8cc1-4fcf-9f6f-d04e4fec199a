<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('assessment_multiple_selects', function (Blueprint $table) {
            $table->dropColumn('shuffle_options');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assessment_multiple_selects', function (Blueprint $table) {
            $table->boolean('shuffle_options')->default(false);
        });
    }
};
