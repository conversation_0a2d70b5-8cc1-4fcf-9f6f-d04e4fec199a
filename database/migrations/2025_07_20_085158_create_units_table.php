<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('units', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('course_id');
            $table->string('title');
            $table->text('description')->nullable();
            $table->enum('skill_type', ['vocabulary', 'grammar', 'listening', 'reading', 'speaking', 'writing']);
            $table->enum('difficulty', ['beginner', 'elementary', 'intermediate', 'upper_intermediate', 'advanced', 'proficient']);
            $table->integer('unit_order');
            $table->timestamps();

            $table->unique(['course_id', 'unit_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('units');
    }
};
