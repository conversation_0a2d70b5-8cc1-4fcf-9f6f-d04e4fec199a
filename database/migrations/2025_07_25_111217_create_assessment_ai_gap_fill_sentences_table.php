<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assessment_ai_gap_fill_sentences', function (Blueprint $table) {
            $table->id();
            $table->string('question'); // The question/instruction for the gap fill
            $table->json('fill_position'); // Array of character positions to fill
            $table->string('context'); // The sentence/context with the gaps
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assessment_ai_gap_fill_sentences');
    }
};
