<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('assessment_ai_gap_fill_sentences', function (Blueprint $table) {
            // Change fill_position from string to JSON to properly store array data
            $table->json('fill_position')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assessment_ai_gap_fill_sentences', function (Blueprint $table) {
            // Revert back to string type
            $table->string('fill_position')->change();
        });
    }
};
