<?php

namespace Database\Seeders;

use App\Models\Assessment;
use App\Models\AssessmentMultipleSelect;
use App\Models\Unit;
use Illuminate\Database\Seeder;

class AssessmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $units = Unit::with('course')->get();

        foreach ($units as $unit) {
            $assessments = $this->getAssessmentsForUnit($unit);
            
            foreach ($assessments as $index => $assessmentData) {
                // Create the AssessmentMultipleSelect record
                $multipleSelect = AssessmentMultipleSelect::create([
                    'question' => $assessmentData['question'],
                    'answer_list' => $assessmentData['answer_list'],
                    'correct_answer_indexes' => $assessmentData['correct_answer_indexes'],
                    'explanations' => $assessmentData['explanations'],
                ]);

                // Create the polymorphic Assessment record
                $assessment = Assessment::create([
                    'itemable_type' => AssessmentMultipleSelect::class,
                    'itemable_id' => $multipleSelect->id,
                ]);

                // Link the assessment to the unit with order
                $unit->assessments()->attach($assessment->id, [
                    'assessment_order' => $index + 1,
                ]);
            }
        }
    }

    private function getAssessmentsForUnit(Unit $unit): array
    {
        $courseTitle = $unit->course->title;
        $unitTitle = $unit->title;

        // Generate assessments based on course and unit
        switch ($courseTitle) {
            case 'English Basics':
                return $this->getBasicEnglishAssessments($unitTitle);
            case 'Intermediate English':
                return $this->getIntermediateEnglishAssessments($unitTitle);
            case 'Advanced English':
                return $this->getAdvancedEnglishAssessments($unitTitle);
            case 'Business English':
                return $this->getBusinessEnglishAssessments($unitTitle);
            case 'English for Travel':
                return $this->getTravelEnglishAssessments($unitTitle);
            case 'Academic English':
                return $this->getAcademicEnglishAssessments($unitTitle);
            default:
                return [];
        }
    }

    private function getBasicEnglishAssessments(string $unitTitle): array
    {
        switch ($unitTitle) {
            case 'Unit 1: Basic Greetings':
                return [
                    [
                        'question' => 'What is the most common greeting in English?',
                        'answer_list' => ['Hello', 'Goodbye', 'Please', 'Thank you'],
                        'correct_answer_indexes' => [0],
                        'explanations' => ['Hello is the most universal and commonly used greeting in English.'],
                    ],
                    [
                        'question' => 'How do you respond to "How are you?"',
                        'answer_list' => ['I am fine, thank you', 'Goodbye', 'Hello', 'Please'],
                        'correct_answer_indexes' => [0],
                        'explanations' => ['This is the standard polite response to asking about someone\'s well-being.'],
                    ],
                    [
                        'question' => 'What do you say when leaving someone?',
                        'answer_list' => ['Hello', 'Goodbye', 'Please', 'Sorry'],
                        'correct_answer_indexes' => [1],
                        'explanations' => ['Goodbye is used when parting or leaving someone.'],
                    ],
                ];

            case 'Unit 2: Numbers 1-100':
                return [
                    [
                        'question' => 'What comes after nineteen?',
                        'answer_list' => ['Twenty', 'Eighteen', 'Thirty', 'Twelve'],
                        'correct_answer_indexes' => [0],
                        'explanations' => ['Twenty follows nineteen in the counting sequence.'],
                    ],
                    [
                        'question' => 'How do you write the number 15?',
                        'answer_list' => ['Fifty', 'Fifteen', 'Fourteen', 'Sixteen'],
                        'correct_answer_indexes' => [1],
                        'explanations' => ['Fifteen is the correct spelling for the number 15.'],
                    ],
                    [
                        'question' => 'What is 10 + 10?',
                        'answer_list' => ['Thirty', 'Twenty', 'Forty', 'Fifty'],
                        'correct_answer_indexes' => [1],
                        'explanations' => ['Ten plus ten equals twenty.'],
                    ],
                ];

            case 'Unit 3: Simple Present Tense':
                return [
                    [
                        'question' => 'Choose the correct form: "She _____ to school every day."',
                        'answer_list' => ['go', 'goes', 'going', 'went'],
                        'correct_answer_indexes' => [1],
                        'explanations' => ['With third person singular (she), we add -s to the verb in simple present.'],
                    ],
                    [
                        'question' => 'Which sentence is correct?',
                        'answer_list' => ['I am eat breakfast', 'I eat breakfast', 'I eating breakfast', 'I eats breakfast'],
                        'correct_answer_indexes' => [1],
                        'explanations' => ['Simple present tense uses the base form of the verb with I.'],
                    ],
                ];

            case 'Unit 4: Family Members':
                return [
                    [
                        'question' => 'What do you call your father\'s brother?',
                        'answer_list' => ['Cousin', 'Uncle', 'Nephew', 'Grandfather'],
                        'correct_answer_indexes' => [1],
                        'explanations' => ['Your father\'s brother is your uncle.'],
                    ],
                    [
                        'question' => 'What do you call your sister\'s daughter?',
                        'answer_list' => ['Niece', 'Nephew', 'Cousin', 'Aunt'],
                        'correct_answer_indexes' => [0],
                        'explanations' => ['Your sister\'s daughter is your niece.'],
                    ],
                ];

            case 'Unit 5: Basic Listening':
                return [
                    [
                        'question' => 'When someone says "Excuse me", what are they usually doing?',
                        'answer_list' => ['Asking for attention', 'Saying goodbye', 'Greeting someone', 'Thanking someone'],
                        'correct_answer_indexes' => [0],
                        'explanations' => ['Excuse me is commonly used to politely get someone\'s attention.'],
                    ],
                ];

            default:
                return [];
        }
    }

    private function getIntermediateEnglishAssessments(string $unitTitle): array
    {
        switch ($unitTitle) {
            case 'Unit 1: Past Tense Forms':
                return [
                    [
                        'question' => 'What is the past tense of "go"?',
                        'answer_list' => ['goed', 'went', 'gone', 'going'],
                        'correct_answer_indexes' => [1],
                        'explanations' => ['Went is the irregular past tense form of go.'],
                    ],
                    [
                        'question' => 'Choose the correct sentence:',
                        'answer_list' => ['I have saw the movie', 'I seen the movie', 'I saw the movie', 'I have see the movie'],
                        'correct_answer_indexes' => [2],
                        'explanations' => ['I saw the movie uses the correct simple past tense form.'],
                    ],
                ];

            case 'Unit 2: Travel Vocabulary':
                return [
                    [
                        'question' => 'Where do you check in for a flight?',
                        'answer_list' => ['Gate', 'Airport', 'Check-in counter', 'Baggage claim'],
                        'correct_answer_indexes' => [2],
                        'explanations' => ['You check in for flights at the check-in counter.'],
                    ],
                ];

            default:
                return [];
        }
    }

    private function getAdvancedEnglishAssessments(string $unitTitle): array
    {
        switch ($unitTitle) {
            case 'Unit 1: Complex Grammar Structures':
                return [
                    [
                        'question' => 'Choose the correct conditional: "If I _____ rich, I would travel the world."',
                        'answer_list' => ['am', 'was', 'were', 'will be'],
                        'correct_answer_indexes' => [2],
                        'explanations' => ['In second conditional, we use were with all subjects for the subjunctive mood.'],
                    ],
                ];

            default:
                return [];
        }
    }

    private function getBusinessEnglishAssessments(string $unitTitle): array
    {
        switch ($unitTitle) {
            case 'Unit 1: Meeting Vocabulary':
                return [
                    [
                        'question' => 'What does "to table a discussion" mean in business?',
                        'answer_list' => ['To start discussing', 'To postpone discussing', 'To end discussing', 'To summarize discussing'],
                        'correct_answer_indexes' => [1],
                        'explanations' => ['To table a discussion means to postpone it for later consideration.'],
                    ],
                ];

            default:
                return [];
        }
    }

    private function getTravelEnglishAssessments(string $unitTitle): array
    {
        switch ($unitTitle) {
            case 'Unit 1: Airport and Flight Vocabulary':
                return [
                    [
                        'question' => 'What do you need to show at airport security?',
                        'answer_list' => ['Passport and boarding pass', 'Only passport', 'Only boarding pass', 'Driver\'s license'],
                        'correct_answer_indexes' => [0],
                        'explanations' => ['You typically need both passport and boarding pass for security screening.'],
                    ],
                ];

            default:
                return [];
        }
    }

    private function getAcademicEnglishAssessments(string $unitTitle): array
    {
        switch ($unitTitle) {
            case 'Unit 1: Research and Citation Skills':
                return [
                    [
                        'question' => 'What is the purpose of citing sources in academic writing?',
                        'answer_list' => ['To make text longer', 'To give credit and avoid plagiarism', 'To confuse readers', 'To show off knowledge'],
                        'correct_answer_indexes' => [1],
                        'explanations' => ['Citations give proper credit to original authors and help avoid plagiarism.'],
                    ],
                ];

            default:
                return [];
        }
    }
}
