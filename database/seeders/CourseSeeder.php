<?php

namespace Database\Seeders;

use App\Models\Course;
use Illuminate\Database\Seeder;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $courses = [
            [
                'title' => 'English Basics',
                'description' => 'Learn fundamental English skills including basic vocabulary, simple grammar, and essential phrases for everyday communication.',
            ],
            [
                'title' => 'Intermediate English',
                'description' => 'Build on your basic English knowledge with more complex grammar structures, expanded vocabulary, and improved listening and reading skills.',
            ],
            [
                'title' => 'Advanced English',
                'description' => 'Master advanced English concepts including complex grammar, academic vocabulary, debate skills, and sophisticated writing techniques.',
            ],
            [
                'title' => 'Business English',
                'description' => 'Develop professional English communication skills for the workplace, including business vocabulary, email writing, presentations, and meeting participation.',
            ],
            [
                'title' => 'English for Travel',
                'description' => 'Essential English phrases and vocabulary for travelers, covering airport procedures, hotel bookings, restaurant orders, and tourist activities.',
            ],
            [
                'title' => 'Academic English',
                'description' => 'Prepare for academic success with advanced reading comprehension, essay writing, research skills, and formal presentation techniques.',
            ],
        ];

        foreach ($courses as $courseData) {
            Course::create($courseData);
        }
    }
}
