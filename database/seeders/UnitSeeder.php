<?php

namespace Database\Seeders;

use App\Models\Course;
use App\Models\Unit;
use App\Enums\UnitType;
use Illuminate\Database\Seeder;

class UnitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $courses = Course::all();

        foreach ($courses as $course) {
            $units = $this->getUnitsForCourse($course->title);
            
            foreach ($units as $index => $unitData) {
                Unit::create([
                    'course_id' => $course->id,
                    'title' => $unitData['title'],
                    'description' => $unitData['description'],
                    'skill_type' => $unitData['skill_type'],
                    'difficulty' => $unitData['difficulty'],
                    'unit_type' => UnitType::MULTIPLE_SELECT,
                    'unit_order' => $index + 1,
                ]);
            }
        }
    }

    private function getUnitsForCourse(string $courseTitle): array
    {
        switch ($courseTitle) {
            case 'English Basics':
                return [
                    [
                        'title' => 'Unit 1: Basic Greetings',
                        'description' => 'Learn essential greeting phrases and polite expressions for daily interactions.',
                        'skill_type' => 'vocabulary',
                        'difficulty' => 'beginner',
                    ],
                    [
                        'title' => 'Unit 2: Numbers 1-100',
                        'description' => 'Master counting from 1 to 100 and basic number-related vocabulary.',
                        'skill_type' => 'vocabulary',
                        'difficulty' => 'beginner',
                    ],
                    [
                        'title' => 'Unit 3: Simple Present Tense',
                        'description' => 'Understand and use the simple present tense for daily activities and habits.',
                        'skill_type' => 'grammar',
                        'difficulty' => 'beginner',
                    ],
                    [
                        'title' => 'Unit 4: Family Members',
                        'description' => 'Learn vocabulary related to family relationships and describing family members.',
                        'skill_type' => 'vocabulary',
                        'difficulty' => 'beginner',
                    ],
                    [
                        'title' => 'Unit 5: Basic Listening',
                        'description' => 'Develop listening skills with simple conversations and basic instructions.',
                        'skill_type' => 'listening',
                        'difficulty' => 'beginner',
                    ],
                ];

            case 'Intermediate English':
                return [
                    [
                        'title' => 'Unit 1: Past Tense Forms',
                        'description' => 'Master regular and irregular past tense forms and their usage in context.',
                        'skill_type' => 'grammar',
                        'difficulty' => 'intermediate',
                    ],
                    [
                        'title' => 'Unit 2: Travel Vocabulary',
                        'description' => 'Essential vocabulary for traveling, including transportation, accommodation, and directions.',
                        'skill_type' => 'vocabulary',
                        'difficulty' => 'intermediate',
                    ],
                    [
                        'title' => 'Unit 3: Listening Comprehension',
                        'description' => 'Improve listening skills with conversations, news clips, and interviews.',
                        'skill_type' => 'listening',
                        'difficulty' => 'intermediate',
                    ],
                    [
                        'title' => 'Unit 4: Reading Stories',
                        'description' => 'Develop reading comprehension through short stories and articles.',
                        'skill_type' => 'reading',
                        'difficulty' => 'intermediate',
                    ],
                ];

            case 'Advanced English':
                return [
                    [
                        'title' => 'Unit 1: Complex Grammar Structures',
                        'description' => 'Master advanced grammar including conditionals, subjunctive mood, and complex sentences.',
                        'skill_type' => 'grammar',
                        'difficulty' => 'advanced',
                    ],
                    [
                        'title' => 'Unit 2: Academic Vocabulary',
                        'description' => 'Expand vocabulary with academic and formal language for professional contexts.',
                        'skill_type' => 'vocabulary',
                        'difficulty' => 'advanced',
                    ],
                    [
                        'title' => 'Unit 3: Debate and Discussion Skills',
                        'description' => 'Develop advanced speaking skills for debates, discussions, and presentations.',
                        'skill_type' => 'speaking',
                        'difficulty' => 'advanced',
                    ],
                    [
                        'title' => 'Unit 4: Essay Writing Techniques',
                        'description' => 'Learn advanced writing techniques for essays, reports, and formal documents.',
                        'skill_type' => 'writing',
                        'difficulty' => 'advanced',
                    ],
                ];

            case 'Business English':
                return [
                    [
                        'title' => 'Unit 1: Meeting Vocabulary',
                        'description' => 'Essential vocabulary and phrases for participating in business meetings.',
                        'skill_type' => 'vocabulary',
                        'difficulty' => 'upper_intermediate',
                    ],
                    [
                        'title' => 'Unit 2: Professional Email Writing',
                        'description' => 'Learn to write clear, professional emails for various business situations.',
                        'skill_type' => 'writing',
                        'difficulty' => 'upper_intermediate',
                    ],
                    [
                        'title' => 'Unit 3: Presentation Skills',
                        'description' => 'Develop confidence and skills for delivering effective business presentations.',
                        'skill_type' => 'speaking',
                        'difficulty' => 'upper_intermediate',
                    ],
                    [
                        'title' => 'Unit 4: Business Listening',
                        'description' => 'Improve listening skills for business contexts including calls and meetings.',
                        'skill_type' => 'listening',
                        'difficulty' => 'upper_intermediate',
                    ],
                ];

            case 'English for Travel':
                return [
                    [
                        'title' => 'Unit 1: Airport and Flight Vocabulary',
                        'description' => 'Essential phrases for airport procedures, check-in, and flight experiences.',
                        'skill_type' => 'vocabulary',
                        'difficulty' => 'elementary',
                    ],
                    [
                        'title' => 'Unit 2: Hotel and Accommodation',
                        'description' => 'Learn vocabulary and phrases for booking hotels and dealing with accommodation.',
                        'skill_type' => 'vocabulary',
                        'difficulty' => 'elementary',
                    ],
                    [
                        'title' => 'Unit 3: Restaurant and Food Ordering',
                        'description' => 'Master restaurant vocabulary and phrases for ordering food and drinks.',
                        'skill_type' => 'speaking',
                        'difficulty' => 'elementary',
                    ],
                    [
                        'title' => 'Unit 4: Asking for Directions',
                        'description' => 'Learn to ask for and understand directions in English-speaking countries.',
                        'skill_type' => 'speaking',
                        'difficulty' => 'elementary',
                    ],
                ];

            case 'Academic English':
                return [
                    [
                        'title' => 'Unit 1: Research and Citation Skills',
                        'description' => 'Learn academic research methods and proper citation techniques.',
                        'skill_type' => 'writing',
                        'difficulty' => 'proficient',
                    ],
                    [
                        'title' => 'Unit 2: Critical Reading Skills',
                        'description' => 'Develop advanced reading comprehension and critical analysis skills.',
                        'skill_type' => 'reading',
                        'difficulty' => 'proficient',
                    ],
                    [
                        'title' => 'Unit 3: Academic Presentations',
                        'description' => 'Master formal presentation skills for academic and professional contexts.',
                        'skill_type' => 'speaking',
                        'difficulty' => 'proficient',
                    ],
                    [
                        'title' => 'Unit 4: Academic Listening',
                        'description' => 'Improve listening skills for lectures, seminars, and academic discussions.',
                        'skill_type' => 'listening',
                        'difficulty' => 'proficient',
                    ],
                ];

            default:
                return [];
        }
    }
}
