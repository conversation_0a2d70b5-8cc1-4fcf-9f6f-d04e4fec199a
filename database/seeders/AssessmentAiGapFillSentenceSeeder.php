<?php

namespace Database\Seeders;

use App\Models\AssessmentAiGapFillSentence;
use App\Models\Assessment;
use App\Models\Unit;
use App\Enums\SkillType;
use App\Enums\UnitType;
use App\Enums\Difficulty;
use Illuminate\Database\Seeder;

class AssessmentAiGapFillSentenceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create units for different skill types and difficulties
        $units = $this->createUnitsForAssessments();

        // Create assessments grouped by unit
        $this->createAssessmentsForUnits($units);

        $this->command->info('Created AI Gap Fill Sentence assessments with associated units');
    }

    /**
     * Create units for different skill type and difficulty combinations
     */
    private function createUnitsForAssessments(): array
    {
        $unitDefinitions = [
            // Grammar Units
            [
                'course_title' => 'English Basics',
                'title' => 'Basic Grammar - Gap Fill',
                'description' => 'Practice basic grammar structures using gap fill exercises.',
                'skill_type' => SkillType::GRAMMAR,
                'difficulty' => Difficulty::BEGINNER,
            ],
            [
                'course_title' => 'Intermediate English',
                'title' => 'Elementary Grammar - Gap Fill',
                'description' => 'Improve grammar skills with elementary level gap fill exercises.',
                'skill_type' => SkillType::GRAMMAR,
                'difficulty' => Difficulty::ELEMENTARY,
            ],
            [
                'course_title' => 'Intermediate English',
                'title' => 'Intermediate Grammar - Gap Fill',
                'description' => 'Master intermediate grammar through contextual gap fill exercises.',
                'skill_type' => SkillType::GRAMMAR,
                'difficulty' => Difficulty::INTERMEDIATE,
            ],
            [
                'course_title' => 'Advanced English',
                'title' => 'Advanced Grammar - Gap Fill',
                'description' => 'Perfect advanced grammar with sophisticated gap fill exercises.',
                'skill_type' => SkillType::GRAMMAR,
                'difficulty' => Difficulty::ADVANCED,
            ],

            // Vocabulary Units
            [
                'course_title' => 'English Basics',
                'title' => 'Basic Vocabulary - Gap Fill',
                'description' => 'Build fundamental vocabulary through gap fill practice.',
                'skill_type' => SkillType::VOCABULARY,
                'difficulty' => Difficulty::BEGINNER,
            ],
            [
                'course_title' => 'Intermediate English',
                'title' => 'Elementary Vocabulary - Gap Fill',
                'description' => 'Expand vocabulary with elementary level gap fill exercises.',
                'skill_type' => SkillType::VOCABULARY,
                'difficulty' => Difficulty::ELEMENTARY,
            ],
            [
                'course_title' => 'Intermediate English',
                'title' => 'Intermediate Vocabulary - Gap Fill',
                'description' => 'Develop intermediate vocabulary through contextual exercises.',
                'skill_type' => SkillType::VOCABULARY,
                'difficulty' => Difficulty::INTERMEDIATE,
            ],
            [
                'course_title' => 'Advanced English',
                'title' => 'Upper-Intermediate Vocabulary - Gap Fill',
                'description' => 'Master upper-intermediate vocabulary with challenging exercises.',
                'skill_type' => SkillType::VOCABULARY,
                'difficulty' => Difficulty::UPPER_INTERMEDIATE,
            ],

            // Reading Units
            [
                'course_title' => 'Academic English',
                'title' => 'Reading Comprehension - Gap Fill',
                'description' => 'Improve reading skills through gap fill comprehension exercises.',
                'skill_type' => SkillType::READING,
                'difficulty' => Difficulty::INTERMEDIATE,
            ],
            [
                'course_title' => 'Academic English',
                'title' => 'Advanced Reading - Gap Fill',
                'description' => 'Develop advanced reading skills with complex gap fill texts.',
                'skill_type' => SkillType::READING,
                'difficulty' => Difficulty::UPPER_INTERMEDIATE,
            ],

            // Writing Units
            [
                'course_title' => 'Academic English',
                'title' => 'Academic Writing - Gap Fill',
                'description' => 'Practice academic writing patterns through gap fill exercises.',
                'skill_type' => SkillType::WRITING,
                'difficulty' => Difficulty::INTERMEDIATE,
            ],
            [
                'course_title' => 'English Basics',
                'title' => 'Basic Writing - Gap Fill',
                'description' => 'Learn basic writing expressions through gap fill practice.',
                'skill_type' => SkillType::WRITING,
                'difficulty' => Difficulty::ELEMENTARY,
            ],
        ];

        $createdUnits = [];

        foreach ($unitDefinitions as $unitData) {
            // Find the course by title
            $course = \App\Models\Course::where('title', $unitData['course_title'])->first();

            if (!$course) {
                $this->command->warn("Course '{$unitData['course_title']}' not found. Skipping unit creation.");
                continue;
            }

            // Get the next unit_order for this course
            $unitOrder = Unit::where('course_id', $course->id)->max('unit_order') + 1;

            $unit = Unit::create([
                'course_id' => $course->id,
                'title' => $unitData['title'],
                'description' => $unitData['description'],
                'skill_type' => $unitData['skill_type'],
                'difficulty' => $unitData['difficulty'],
                'unit_type' => UnitType::AI_GAP_FILL_SENTENCE,
                'unit_order' => $unitOrder,
            ]);

            $createdUnits[] = $unit;

            $this->command->info("Created unit: {$unit->title}");
        }

        return $createdUnits;
    }

    /**
     * Create assessments for each unit
     */
    private function createAssessmentsForUnits(array $units): void
    {
        $assessmentsByUnit = [
            // Grammar Assessments
            'Basic Grammar - Gap Fill' => [
                [
                    'question' => 'The cat ____ on the mat.',
                    'context' => 'A simple sentence about a cat\'s location.',
                ],
                [
                    'question' => 'I ____ breakfast every morning.',
                    'context' => 'A daily routine activity.',
                ],
                [
                    'question' => 'She _____ to school by bus.',
                    'context' => 'Transportation method for getting to school.',
                ],
            ],
            'Elementary Grammar - Gap Fill' => [
                [
                    'question' => 'Yesterday I ____ to the park and _____ my friends.',
                    'context' => 'A past activity involving social interaction.',
                ],
                [
                    'question' => 'My brother _____ football every ______ after school.',
                    'context' => 'Regular sports activity schedule.',
                ],
            ],
            'Intermediate Grammar - Gap Fill' => [
                [
                    'question' => 'If I _____ enough money, I _____ buy a new car.',
                    'context' => 'A conditional statement about purchasing power.',
                ],
                [
                    'question' => 'Although it was ______, they _______ to complete the project.',
                    'context' => 'Overcoming challenges to achieve goals.',
                ],
            ],
            'Advanced Grammar - Gap Fill' => [
                [
                    'question' => 'Having _______ the presentation, she _______ confident about the meeting.',
                    'context' => 'Preparation leading to confidence in professional settings.',
                ],
                [
                    'question' => 'Were it not for the _______ intervention, the situation _______ have deteriorated beyond repair.',
                    'context' => 'Hypothetical scenarios and their consequences.',
                ],
            ],

            // Vocabulary Assessments
            'Basic Vocabulary - Gap Fill' => [
                [
                    'question' => 'I like to eat _____ for breakfast.',
                    'context' => 'Common breakfast foods vocabulary.',
                ],
                [
                    'question' => 'The _____ is very hot today.',
                    'context' => 'Weather-related vocabulary.',
                ],
            ],
            'Elementary Vocabulary - Gap Fill' => [
                [
                    'question' => 'The weather is _____ today, so we can _____ outside.',
                    'context' => 'Weather conditions affecting outdoor activities.',
                ],
                [
                    'question' => 'My family _____ to the beach every _____.',
                    'context' => 'Family activities and time expressions.',
                ],
            ],
            'Intermediate Vocabulary - Gap Fill' => [
                [
                    'question' => 'The scientist _______ that the experiment _____ successful.',
                    'context' => 'Research results and scientific conclusions.',
                ],
                [
                    'question' => 'The manager _______ the proposal after careful _______.',
                    'context' => 'Business decision-making vocabulary.',
                ],
            ],
            'Upper-Intermediate Vocabulary - Gap Fill' => [
                [
                    'question' => 'The company\'s _______ strategy has _______ significantly over the past decade.',
                    'context' => 'Business development and strategic changes.',
                ],
                [
                    'question' => 'The research _______ several _______ findings about climate change.',
                    'context' => 'Academic research and environmental vocabulary.',
                ],
            ],

            // Reading Assessments
            'Reading Comprehension - Gap Fill' => [
                [
                    'question' => 'The author _______ that modern technology has _______ the way we communicate.',
                    'context' => 'Literary analysis of technological impact on communication.',
                ],
                [
                    'question' => 'The main _______ of the article is to _______ the benefits of exercise.',
                    'context' => 'Text analysis and comprehension skills.',
                ],
            ],
            'Advanced Reading - Gap Fill' => [
                [
                    'question' => 'According to the passage, the _______ causes of climate change _______ human activities.',
                    'context' => 'Scientific text about environmental issues.',
                ],
                [
                    'question' => 'The author\'s _______ is clearly _______ through sophisticated argumentation.',
                    'context' => 'Advanced text analysis and critical reading.',
                ],
            ],

            // Writing Assessments
            'Academic Writing - Gap Fill' => [
                [
                    'question' => 'In conclusion, the _______ evidence _______ that further research is necessary.',
                    'context' => 'Academic writing conclusion patterns.',
                ],
                [
                    'question' => 'The study _______ significant _______ for future policy development.',
                    'context' => 'Academic research implications and recommendations.',
                ],
            ],
            'Basic Writing - Gap Fill' => [
                [
                    'question' => '_______, I would like to _______ my sincere gratitude for your support.',
                    'context' => 'Formal letter closing expressions.',
                ],
                [
                    'question' => 'Dear Sir or Madam, I am _______ to _______ about your services.',
                    'context' => 'Formal letter opening expressions.',
                ],
            ],
        ];

        foreach ($units as $unit) {
            if (!isset($assessmentsByUnit[$unit->title])) {
                $this->command->warn("No assessments defined for unit: {$unit->title}");
                continue;
            }

            $assessments = $assessmentsByUnit[$unit->title];
            $assessmentOrder = 1;

            foreach ($assessments as $assessmentData) {
                // Create the AssessmentAiGapFillSentence
                $gapFillAssessment = AssessmentAiGapFillSentence::create([
                    'question' => $assessmentData['question'],
                    'context' => $assessmentData['context'],
                ]);

                // Create the polymorphic Assessment record
                $assessment = Assessment::create([
                    'itemable_type' => AssessmentAiGapFillSentence::class,
                    'itemable_id' => $gapFillAssessment->id,
                ]);

                // Attach assessment to unit with proper ordering
                $assessment->units()->attach($unit->id, [
                    'assessment_order' => $assessmentOrder++
                ]);

                $this->command->info("Created assessment for unit '{$unit->title}': {$assessmentData['question']}");
            }
        }
    }
}
