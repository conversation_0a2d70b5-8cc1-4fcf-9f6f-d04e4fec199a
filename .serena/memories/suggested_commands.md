# Essential Development Commands

## Docker Container Commands
**IMPORTANT**: All artisan commands must be run inside the Docker container using `docker exec`

### Basic Docker Operations
```bash
# Start all services
docker-compose up -d

# Stop all services
docker-compose down

# View backend logs
docker-compose logs -f backend

# Access backend container shell
docker-compose exec backend bash
```

### Artisan Commands (Inside Container)
```bash
# Run migrations
docker exec backend-api php artisan migrate

# Rollback migrations
docker exec backend-api php artisan migrate:rollback

# Fresh migration (WARNING: destroys data)
docker exec backend-api php artisan migrate:fresh

# Interactive shell
docker exec backend-api php artisan tinker

# Clear application cache
docker exec backend-api php artisan cache:clear
docker exec backend-api php artisan config:clear
```

### Laravel Octane Commands
**CRITICAL**: Octane keeps application state in memory. Always reload after code changes!
```bash
# Reload Octane workers (after code changes)
docker exec backend-api php artisan octane:reload

# Check Octane status
docker exec backend-api php artisan octane:status

# Stop/Start Octane
docker exec backend-api php artisan octane:stop
docker exec backend-api php artisan octane:start
```

## Testing Commands
```bash
# Run all tests
docker exec backend-api php artisan test

# Run specific test suite
docker exec backend-api php artisan test --testsuite=Feature
docker exec backend-api php artisan test --testsuite=Unit

# Run tests with coverage
docker exec backend-api php artisan test --coverage
```

## Code Quality Commands
```bash
# Format code with Laravel Pint
docker exec backend-api ./vendor/bin/pint

# Check code formatting without fixing
docker exec backend-api ./vendor/bin/pint --test
```

## Database Commands
```bash
# Access MariaDB from container
docker exec backend-mariadb mysql -u root -p backend

# Access MariaDB from host
mysql -h 127.0.0.1 -P 3307 -u root -p backend

# phpMyAdmin web interface
# http://localhost:8080 (root/secret)
```

## Service URLs
- **Backend API**: http://localhost:8000
- **phpMyAdmin**: http://localhost:8080
- **MariaDB**: localhost:3307
- **Redis**: localhost:6380