# English Game Backend - Project Overview

## Purpose
This is a Laravel-based REST API backend for an English learning game platform. The system manages educational content including courses, units, and various types of assessments (multiple choice, AI-generated gap fill sentences).

## Key Features
- **Course Management**: Hierarchical course structure with units and assessments
- **Assessment Types**: Multiple choice and AI-generated gap fill sentence assessments
- **Content Generation**: Integration with Google Gemini AI for content enhancement
- **CSV Import**: Bulk import functionality for assessments
- **Authentication**: Staff authentication system with role-based access
- **Polymorphic Assessments**: Extensible assessment system using polymorphic relationships

## Domain Models
- **Course**: Top-level learning container
- **Unit**: Learning units within courses (ordered)
- **Assessment**: Base polymorphic assessment model
- **AssessmentMultipleSelect**: Multiple choice assessment implementation
- **AssessmentAiGapFillSentence**: AI-generated gap fill assessment implementation
- **Staff**: Administrative users

## Key Relationships
- Course → hasMany → Unit (ordered by unit_order)
- Unit → belongsToMany → Assessment (through pivot table, ordered by assessment_order)
- Assessment → morphTo → itemable (polymorphic to specific assessment types)

## Business Logic
- Service layer architecture for complex operations
- Form request validation for API inputs
- Standardized JSON API responses with success/error format
- No foreign key constraints by design for flexibility