# Tech Stack

## Core Framework
- **Laravel 12.0**: PHP web framework
- **PHP 8.2+**: Required PHP version
- **Laravel Octane 2.9**: High-performance application server using FrankenPHP

## Database & Cache
- **MariaDB 10.11**: Primary database
- **Redis 7.2**: Cache and session storage
- **Laravel Eloquent**: ORM for database interactions

## Key Laravel Packages
- **Laravel Sanctum 4.0**: API authentication
- **Laravel Pint 1.13**: Code formatting (PSR-12)
- **Laravel Tinker 2.10**: Interactive REPL
- **BenSampo Laravel Enum 6.12**: Type-safe enums

## AI Integration
- **Google Gemini PHP Laravel 2.0**: AI content generation integration

## Development Tools
- **PHPUnit 11.5**: Testing framework
- **Faker**: Test data generation
- **Laravel Pail 1.2**: Log viewer
- **Collision 8.6**: Error reporting

## Infrastructure
- **Docker & Docker Compose**: Containerized development environment
- **FrankenPHP**: Modern PHP application server
- **phpMyAdmin**: Database management interface

## File Structure
- Standard Laravel 11+ structure
- Service layer in `app/Services/`
- Form requests in `app/Http/Requests/`
- Enums in `app/Enums/`
- Polymorphic models in `app/Models/`