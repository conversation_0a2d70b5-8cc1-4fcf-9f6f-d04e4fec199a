# Codebase Structure

## Laravel Application Structure

### Core Application (`app/`)
```
app/
├── Enums/                    # Type-safe enums
│   ├── Difficulty.php        # Learning difficulty levels
│   └── SkillType.php         # English skill types
├── Http/
│   ├── Controllers/          # API controllers
│   │   ├── Api/              # API-specific controllers
│   │   ├── *Controller.php   # CRUD controllers
│   │   └── Controller.php    # Base controller
│   ├── Middleware/           # Custom middleware
│   │   ├── AdminAuth.php     # Admin authentication
│   │   └── UserAuth.php      # User authentication
│   ├── Requests/             # Form request validation
│   │   ├── Store*Request.php # Create validation
│   │   └── Update*Request.php# Update validation
│   └── Resources/            # API resources
├── Models/                   # Eloquent models
│   ├── Assessment.php        # Base polymorphic model
│   ├── AssessmentMultipleSelect.php
│   ├── AssessmentAiGapFillSentence.php
│   ├── Course.php
│   ├── Unit.php
│   └── Staff.php
└── Services/                 # Business logic layer
    ├── AssessmentService.php
    ├── CourseService.php
    ├── CsvImportService.php
    └── GeminiService.php
```

### Database (`database/`)
```
database/
├── migrations/               # Database schema changes
├── seeders/                  # Test data seeders
└── factories/                # Model factories for testing
```

### Configuration & Infrastructure
```
├── config/                   # Laravel configuration
├── routes/
│   └── api.php              # API routes definition
├── tests/
│   ├── Feature/             # Integration tests
│   └── Unit/                # Unit tests
├── docker-compose.yml       # Docker services
├── Dockerfile               # Laravel container
└── CLAUDE.md               # Development notes
```

## Key Architecture Patterns

### Polymorphic Assessment System
- `Assessment` (base model) → `morphTo` → specific assessment types
- `AssessmentMultipleSelect` → `morphOne` → `Assessment`
- `AssessmentAiGapFillSentence` → `morphOne` → `Assessment`

### Service Layer Architecture
- Controllers handle HTTP concerns
- Services handle business logic
- Models handle data relationships
- Form Requests handle validation

### API Structure
- `/api/public/*` - Public endpoints
- `/api/admin/*` - Admin-protected endpoints
- `/api/auth/*` - Authentication endpoints
- Standardized JSON response format

### Data Flow
1. Route → Controller → Service → Model → Database
2. Request validation via Form Request classes
3. Business logic encapsulated in Service classes
4. Consistent API responses with success/error format