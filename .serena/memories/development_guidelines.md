# Development Guidelines & Best Practices

## Laravel Octane Considerations
**CRITICAL**: This application uses Laravel Octane with FrankenPHP
- Application state persists in memory between requests
- Always run `docker exec backend-api php artisan octane:reload` after code changes
- Model instances may persist between requests
- Use `Model::clearBootedModels()` to clear cached model state when testing

## Service Layer Guidelines

### When to Use Services
- **Create/Update/Delete operations** → Use dedicated Service classes
- **Show/List operations** → Direct controller implementation (simple data retrieval)
- **Complex business logic** → Always encapsulate in Service layer

### Service Pattern Example
```php
class ExampleController extends Controller
{
    public function __construct(private ExampleService $service) {}

    // Simple - direct implementation
    public function index() { return ExampleModel::paginate(); }
    public function show(ExampleModel $model) { return $model; }

    // Complex - use service
    public function store(StoreExampleRequest $request) {
        return $this->service->create($request->validated());
    }
}
```

## API Design Standards

### Response Format (MANDATORY)
```php
// Success responses
return response()->json([
    'success' => true,
    'message' => 'Operation completed successfully',
    'data' => $responseData
], $statusCode);

// Error responses
return response()->json([
    'success' => false,
    'message' => 'Error description',
    'error' => $errorDetails // Optional
], $errorStatusCode);
```

### Validation
- Use Form Request classes for all API validation
- Follow Laravel validation conventions
- Provide clear error messages

## Database Design Principles

### Key Decisions
- **No Foreign Key Constraints**: By design for flexibility
- **Polymorphic Relationships**: Used for extensible assessment system
- **JSON Storage**: For complex data structures (answer_list, explanations)
- **Explicit Ordering**: Using `unit_order`, `assessment_order` fields

### Model Relationships
- Use Laravel's relationship methods consistently
- Follow naming conventions (singular for belongsTo, plural for hasMany)
- Load relationships efficiently to avoid N+1 queries

## Testing Guidelines
- Write Feature tests for API endpoints
- Write Unit tests for Service layer logic
- Use model factories for test data
- Test both success and error scenarios

## Security Considerations
- Use Laravel Sanctum for API authentication
- Validate all inputs via Form Requests
- Follow Laravel security best practices
- Never expose sensitive data in API responses

## Performance Guidelines
- Use eager loading for relationships
- Implement pagination for list endpoints
- Use Redis for caching and sessions
- Monitor Octane memory usage

## Code Organization
- Follow PSR-12 coding standards
- Use Laravel Pint for consistent formatting
- Group related functionality in Services
- Keep Controllers thin and focused on HTTP concerns