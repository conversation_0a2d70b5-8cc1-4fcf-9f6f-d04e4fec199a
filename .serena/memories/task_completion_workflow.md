# Task Completion Workflow

## Post-Development Checklist

### 1. Code Quality
```bash
# Format code with Laravel Pint
docker exec backend-api ./vendor/bin/pint

# Verify formatting
docker exec backend-api ./vendor/bin/pint --test
```

### 2. Testing
```bash
# Run all tests
docker exec backend-api php artisan test

# Run specific test suites if needed
docker exec backend-api php artisan test --testsuite=Feature
docker exec backend-api php artisan test --testsuite=Unit
```

### 3. Database Operations
```bash
# Run any new migrations
docker exec backend-api php artisan migrate

# Clear application caches
docker exec backend-api php artisan config:clear
docker exec backend-api php artisan cache:clear
```

### 4. Laravel Octane Considerations
**CRITICAL**: After any code changes, always reload Octane workers:
```bash
docker exec backend-api php artisan octane:reload
```

### 5. Model Testing (if model changes made)
```bash
# Clear model cache and test
docker exec backend-api php artisan tinker --execute="
\Illuminate\Database\Eloquent\Model::clearBootedModels();
// Test your model changes here
"
```

## Development Workflow
1. Make code changes
2. Run `docker exec backend-api php artisan octane:reload`
3. Test changes manually or with automated tests
4. Run code formatting: `docker exec backend-api ./vendor/bin/pint`
5. Run tests: `docker exec backend-api php artisan test`
6. Clear caches if needed
7. Commit changes

## Important Notes
- **Always use Docker**: Never run artisan commands directly on host
- **Octane Memory**: Application state persists between requests - always reload after changes
- **No Foreign Keys**: Database design intentionally avoids foreign key constraints
- **Service Layer**: Use services for complex operations, direct controllers for simple CRUD
- **API Response Format**: Always use standardized success/error response format