# Coding Style & Conventions

## Code Formatting
- **Laravel Pint**: Uses PSR-12 standard (configured in composer.json)
- **EditorConfig**: Consistent editor settings (.editorconfig present)
- **No custom Pint config**: Uses Laravel Pint defaults

## Laravel Conventions
- **PSR-4 Autoloading**: Standard Laravel namespace structure
- **Eloquent Models**: 
  - Use `$fillable` arrays for mass assignment
  - Use `$casts` for attribute casting
  - Follow Laravel relationship naming conventions
- **Controllers**: 
  - Extend base `Controller` class
  - Use dependency injection in constructors
  - Follow RESTful naming (index, show, store, update, destroy)

## Architecture Patterns
### Service Layer Pattern
- **Complex Operations**: Use dedicated Service classes (create/update/delete)
- **Simple Operations**: Direct controller implementation (show/list)
- **Constructor Injection**: Services injected via constructor
- **Example**: `AssessmentMultipleSelectService` for business logic

### API Response Format
All API responses follow standardized format:
```php
// Success
{
    "success": true,
    "message": "Operation completed successfully",
    "data": $responseData
}

// Error
{
    "success": false,
    "message": "Error description",
    "error": $errorDetails // optional
}
```

### Validation
- **Form Request Classes**: Used for API validation
- **Example**: `StoreAssessmentMultipleSelectRequest`, `UpdateUnitRequest`

## Database Conventions
- **No Foreign Key Constraints**: By design for flexibility
- **Polymorphic Relationships**: Used for extensible assessment system
- **Ordering Fields**: `unit_order`, `assessment_order` for explicit ordering
- **JSON Storage**: Used for complex data structures (answer_list, explanations)

## Naming Conventions
- **Models**: Singular, PascalCase (`Assessment`, `Unit`)
- **Controllers**: PascalCase with Controller suffix
- **Services**: PascalCase with Service suffix
- **Database Tables**: Snake_case, plural (`assessment_multiple_selects`)
- **Migrations**: Descriptive names with timestamps