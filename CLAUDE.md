# English Game Backend - Claude Development Notes

## Project Setup

This Laravel application runs with **Laravel Octane** in Docker containers.

## Important Development Commands

### Running Artisan Commands
Always run commands inside the Docker container:
```bash
docker exec backend-api php artisan [command]
```

### Database Operations
```bash
# Run migrations
docker exec backend-api php artisan migrate

# Rollback migrations
docker exec backend-api php artisan migrate:rollback

# Fresh migration (WARNING: destroys data)
docker exec backend-api php artisan migrate:fresh

# Tinker (interactive shell)
docker exec backend-api php artisan tinker
```

### Octane Management
```bash
# Reload Octane workers (after code changes)
docker exec backend-api php artisan octane:reload

# Check Octane status
docker exec backend-api php artisan octane:status

# Stop Octane
docker exec backend-api php artisan octane:stop

# Start Octane
docker exec backend-api php artisan octane:start
```

## ⚠️ Octane Cache Considerations

**CRITICAL**: Laravel Octane keeps application state in memory between requests. Be careful about:

### Model Caching
- Model instances may persist between requests
- Always reload models when testing changes
- Use `Model::clearBootedModels()` to clear cached model state

### Development Workflow
1. Make model/code changes
2. Run `docker exec backend-api php artisan octane:reload`
3. Test changes to ensure fresh state

### Testing Models
```bash
# Clear model cache before testing
docker exec backend-api php artisan tinker --execute="
\Illuminate\Database\Eloquent\Model::clearBootedModels();
// your test code here
"
```

## Database Schema

### Models Created
- `Course` - Main course container
- `Unit` - Learning units within courses
- `Assessment` - Base polymorphic assessment table
- `AssessmentMultipleSelect` - Multiple-choice assessment type with JSON data storage
- `UnitAssessment` - Junction table for unit-assessment relationships

### Key Features
- No foreign key constraints (by design)
- Polymorphic relationships for extensible assessment types
- Proper ordering with `unit_order` and `assessment_order`
- Laravel-style relationships configured
- Clean separation: Assessment is pure polymorphic base, content stored in concrete types

### Enums
- `SkillType`: vocabulary, grammar, listening, reading, speaking, writing
- `Difficulty`: beginner, elementary, intermediate, upper_intermediate, advanced, proficient

## AssessmentMultipleSelect JSON Structure

The `AssessmentMultipleSelect` model stores data in JSON format:

```php
AssessmentMultipleSelect::create([
    'question' => 'Your question text here',
    'answer_list' => [
        'Option 1',
        'Option 2',
        'Option 3',
        'Option 4'
    ],
    'correct_answer_indexes' => [2], // Array of correct answer indexes (0-based)
    'explanations' => [
        'Explanation text for the answer'
    ]
]);
```

### Helpful Methods
- `$assessment->correct_answers` - Get array of correct answer texts
- `$assessment->isCorrectAnswer($index)` - Check if an index is correct
- `$assessment->shuffled_answers` - Get shuffled answer list (always shuffled)

## Model Changes (Latest)

### Removed Fields
- **Assessment**: Removed `stem` and `explanation` (content stored in concrete assessment types)
- **Unit**: Removed `time_limit_seconds` and `points_per_assessment` (moved to business logic layer)

## Entity Relationships

### Core Relationships
- **Course** `hasMany` **Unit** (ordered by `unit_order`)
- **Unit** `belongsToMany` **Assessment** through `unit_assessments` pivot (ordered by `assessment_order`)
- **Assessment** `morphTo` **itemable** (polymorphic relationship to assessment types)
- **AssessmentMultipleSelect** `morphOne` **Assessment** (concrete assessment implementation)

### Data Flow
1. **Course** contains multiple **Units** in order
2. **Unit** contains multiple **Assessments** in order via pivot table
3. **Assessment** is a polymorphic base pointing to specific assessment types
4. **AssessmentMultipleSelect** stores actual question data in JSON format

## Coding Style Guidelines

### Service Layer Architecture
- **Create/Update/Delete operations** → Use dedicated Service classes
- **Show/List operations** → Direct controller implementation (simple data retrieval)
- **Validation** → Use Form Request classes
- **Business Logic** → Always encapsulate in Service layer

### API Structure
```php
// Controller pattern for CRUD
class ExampleController extends Controller
{
    public function __construct(private ExampleService $service) {}

    // Simple data retrieval - direct implementation
    public function index() { return ExampleModel::paginate(); }
    public function show(ExampleModel $model) { return $model; }

    // Complex operations - use service
    public function store(StoreExampleRequest $request) {
        return $this->service->create($request->validated());
    }

    public function update(UpdateExampleRequest $request, ExampleModel $model) {
        return $this->service->update($model, $request->validated());
    }

    public function destroy(ExampleModel $model) {
        return $this->service->delete($model);
    }
}
```

### API Response Format

**All API responses must follow this standardized format:**

```php
// Success responses
return response()->json([
    'success' => true,
    'message' => 'Operation completed successfully',
    'data' => $responseData
], $statusCode);

// Error responses
return response()->json([
    'success' => false,
    'message' => 'Error description',
    'error' => $errorDetails // Optional, for debugging
], $errorStatusCode);
```

**Examples:**
```php
// Create success
{
    "success": true,
    "message": "Assessment created successfully",
    "data": { /* assessment object */ }
}

// Update success
{
    "success": true,
    "message": "Assessment updated successfully",
    "data": { /* updated assessment object */ }
}

// Delete success
{
    "success": true,
    "message": "Assessment deleted successfully",
    "data": null
}

// Error response
{
    "success": false,
    "message": "Failed to update assessment",
    "error": "Validation failed: question is required"
}
```

## Unit CRUD API

### Admin Endpoints (protected)
- `GET /api/admin/units` - List all units with filtering
- `POST /api/admin/units` - Create new unit
- `GET /api/admin/units/{id}` - Show unit details
- `PUT/PATCH /api/admin/units/{id}` - Update unit
- `DELETE /api/admin/units/{id}` - Delete unit
- `GET /api/admin/courses/{courseId}/units` - Get units by course
- `POST /api/admin/units/{id}/duplicate` - Duplicate unit
- `PATCH /api/admin/units/{id}/move-to-course` - Move unit to different course
- `PATCH /api/admin/units/{id}/reorder` - Reorder unit within course

### Public Endpoints
- `GET /api/public/units/{id}` - View unit details
- `GET /api/public/courses/{courseId}/units` - View units by course

### Filtering Parameters
- `course_id` - Filter by course
- `skill_type` - Filter by skill type (vocabulary, grammar, listening, reading, speaking, writing)
- `difficulty` - Filter by difficulty (beginner, elementary, intermediate, upper_intermediate, advanced, proficient)
- `per_page` - Pagination limit (max 100)

### Advanced Features
- **Auto-ordering**: Unit order is automatically assigned if not provided
- **Reordering**: Units are automatically reordered when moved or deleted
- **Duplication**: Copy units with assessments to same or different course
- **Course Migration**: Move units between courses with proper reordering
- **Relationship Loading**: Includes course and assessments in responses

## CSV Import API

### Import Functionality
- **Bulk Import**: Create multiple units and assessments from a single CSV file
- **Validation**: Comprehensive validation before processing
- **Template**: Download sample CSV template
- **Dry Run**: Validate CSV without importing

### Admin Endpoints (protected)
- `POST /api/admin/import/csv` - Import units and assessments from CSV
 - `GET /api/admin/import/template` - Download CSV template

### CSV Format
Required headers:
- `question` - Assessment question (max 1000 chars)
- `option_1` - First answer option (required, max 500 chars)
- `option_2` - Second answer option (required, max 500 chars)
- `option_3` - Third answer option (optional, max 500 chars)
- `option_4` - Fourth answer option (optional, max 500 chars)
Can be more option (should start with option_)
- `correct_answer` - option_1, option_2, option_3, option_4, ... maybe many (separate by comma)
- `explanation` - Answer explanation (optional, max 1000 chars)

### Import Features
- **Unit Grouping**: Multiple assessments with same unit_title create one unit
- **Auto-ordering**: Units and assessments are automatically ordered
- **Transaction Safety**: All-or-nothing import (rollback on any error)
- **File Validation**: 10MB max, CSV/TXT only
- **Data Validation**: Comprehensive validation of all fields and relationships
- **Error Reporting**: Detailed validation errors with row numbers
