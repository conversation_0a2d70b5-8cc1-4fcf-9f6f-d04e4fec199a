# Staff Authentication API Documentation

## Overview

The Staff Authentication API provides secure token-based authentication for administrative staff members. It uses Laravel Sanctum for token management and implements a dual middleware system for enhanced security.

## Base URL
```
http://your-domain.com/api/admin
```

## Endpoints

### 1. Staff Login
Authenticate a staff member and receive an access token.

- **URL**: `/api/admin/login`
- **Method**: `POST`
- **Authentication**: None required (public endpoint with admin.auth middleware)
- **Content-Type**: `application/json`

#### Request Payload
```json
{
    "username": "staff_username",
    "password": "staff_password"
}
```

#### Request Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `username` | string | Yes | Staff member's username |
| `password` | string | Yes | Staff member's password |

#### Success Response (200 OK)
```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "staff": {
            "id": 1,
            "username": "admin_user",
            "created_at": "2024-01-15T10:30:00.000000Z",
            "updated_at": "2024-01-15T10:30:00.000000Z"
        },
        "token": "1|abc123def456ghi789jkl012mno345pqr678stu901vwx234yz"
    }
}
```

#### Error Responses

**Validation Error (422 Unprocessable Entity)**
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "username": ["The username field is required."],
        "password": ["The password field is required."]
    }
}
```

**Invalid Credentials (401 Unauthorized)**
```json
{
    "success": false,
    "message": "Invalid credentials"
}
```

#### cURL Example
```bash
curl -X POST "http://localhost:8000/api/admin/login" \
     -H "Content-Type: application/json" \
     -d '{
         "username": "admin_user",
         "password": "secure_password"
     }'
```

---

### 2. Staff Logout
Logout the authenticated staff member and revoke the current token.

- **URL**: `/api/admin/logout`
- **Method**: `POST`
- **Authentication**: Required (Bearer Token)
- **Middleware**: `auth:staff`

#### Request Headers
```
Authorization: Bearer {your_staff_token}
Content-Type: application/json
```

#### Request Payload
No payload required.

#### Success Response (200 OK)
```json
{
    "success": true,
    "message": "Logged out successfully"
}
```

#### Error Responses

**No Token (401 Unauthorized)**
```json
{
    "success": false,
    "message": "Unauthenticated."
}
```

**Invalid/Expired Token (401 Unauthorized)**
```json
{
    "success": false,
    "message": "Unauthenticated."
}
```

#### cURL Example
```bash
curl -X POST "http://localhost:8000/api/admin/logout" \
     -H "Authorization: Bearer 1|abc123def456ghi789jkl012mno345pqr678stu901vwx234yz" \
     -H "Content-Type: application/json"
```

---

### 3. Get Staff Profile
Retrieve the authenticated staff member's profile information.

- **URL**: `/api/admin/me`
- **Method**: `GET`
- **Authentication**: Required (Bearer Token)
- **Middleware**: `auth:staff`

#### Request Headers
```
Authorization: Bearer {your_staff_token}
Accept: application/json
```

#### Success Response (200 OK)
```json
{
    "success": true,
    "message": "Staff information",
    "data": {
        "id": 1,
        "username": "admin_user",
        "created_at": "2024-01-15T10:30:00.000000Z",
        "updated_at": "2024-01-15T10:30:00.000000Z"
    }
}
```

#### Error Responses

**No Token (401 Unauthorized)**
```json
{
    "success": false,
    "message": "Unauthenticated."
}
```

**Invalid/Expired Token (401 Unauthorized)**
```json
{
    "success": false,
    "message": "Unauthenticated."
}
```

#### cURL Example
```bash
curl -X GET "http://localhost:8000/api/admin/me" \
     -H "Authorization: Bearer 1|abc123def456ghi789jkl012mno345pqr678stu901vwx234yz" \
     -H "Accept: application/json"
```

## Authentication Flow

### 1. Login Process
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Database

    Client->>API: POST /api/admin/login
    API->>Database: Validate credentials
    Database-->>API: Staff found
    API->>Database: Delete old tokens
    API->>Database: Create new token
    API-->>Client: Return staff data + token
```

### 2. Authenticated Request Process
```mermaid
sequenceDiagram
    participant Client
    participant Middleware
    participant Controller

    Client->>Middleware: Request with Bearer token
    Middleware->>Middleware: Validate token
    Middleware->>Controller: Forward request
    Controller-->>Client: Return response
```

## Security Features

### Token Management
- **Automatic Cleanup**: Login deletes all existing tokens for the staff member
- **Single Session**: Only one active token per staff member at a time
- **Secure Logout**: Token is immediately revoked on logout
- **Token Expiration**: Tokens can be configured to expire (see Sanctum config)

### Middleware Protection
- **admin.auth**: Custom middleware for staff-specific authentication
- **auth:staff**: Laravel Sanctum middleware using the 'staff' guard
- **Bearer Token**: All protected endpoints require valid Bearer token

### Password Security
- Passwords are hashed using Laravel's Hash facade
- Secure password verification using `Hash::check()`

## Error Handling

### HTTP Status Codes
| Code | Description | When It Occurs |
|------|-------------|----------------|
| 200 | OK | Successful operation |
| 401 | Unauthorized | Invalid credentials or missing/invalid token |
| 403 | Forbidden | Admin access required (from admin.auth middleware) |
| 422 | Unprocessable Entity | Validation errors |
| 500 | Internal Server Error | Server-side errors |

### Common Error Scenarios

#### 1. Missing Authorization Header
```bash
# Request without token
curl -X GET "http://localhost:8000/api/admin/me"

# Response
{
    "success": false,
    "message": "Unauthenticated."
}
```

#### 2. Invalid Token Format
```bash
# Request with malformed token
curl -X GET "http://localhost:8000/api/admin/me" \
     -H "Authorization: Bearer invalid_token"

# Response
{
    "success": false,
    "message": "Unauthenticated."
}
```

#### 3. Expired/Revoked Token
```bash
# Request with expired token
curl -X GET "http://localhost:8000/api/admin/me" \
     -H "Authorization: Bearer 1|expired_token"

# Response
{
    "success": false,
    "message": "Unauthenticated."
}
```
