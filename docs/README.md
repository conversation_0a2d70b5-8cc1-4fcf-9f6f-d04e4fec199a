# API Documentation

This directory contains comprehensive API documentation for the Mining Backend application.

## Available Documentation

### [Machine API Documentation](machine-api.md)

Complete documentation for the Machine Controller API endpoints, including:

### [Postman Collection](machine-api-postman-collection.json)

Ready-to-import Postman collection with all API endpoints configured for easy testing.

### [OpenAPI/Swagger Specification](machine-api-openapi.yaml)

OpenAPI 3.0 specification file that can be used with Swagger UI or other API documentation tools.

## Documentation Files

| File | Description |
|------|-------------|
| `machine-api.md` | Complete API documentation with examples |
| `machine-api-postman-collection.json` | Postman collection for API testing |
| `machine-api-openapi.yaml` | OpenAPI/Swagger specification |
| `README.md` | This overview file |

## How to Use the Documentation

### 1. **Reading the Documentation**
Start with [machine-api.md](machine-api.md) for complete API documentation with examples and detailed explanations.

### 2. **Testing with Postman**
1. Import `machine-api-postman-collection.json` into Postman
2. Set the `admin_token` variable with your authentication token
3. Test all endpoints with pre-configured requests

### 3. **Using with Swagger/OpenAPI Tools**
1. Use `machine-api-openapi.yaml` with Swagger UI or other OpenAPI tools
2. Generate client SDKs using OpenAPI generators
3. Validate API responses against the specification

### 4. **API Endpoint Categories**

- **Public Endpoints**: For users to view available machines
  - List active machines with search and pagination
  - View individual machine details

- **Admin Endpoints**: For administrators to manage machines
  - List all machines (active and inactive)
  - Create new machines
  - Update existing machines
  - View any machine details

## Quick Start

### Public API Usage

No authentication required for public endpoints:

```bash
# List all active machines
curl -X GET "http://localhost:8000/api/public/machines"

# Search for specific machines
curl -X GET "http://localhost:8000/api/public/machines?search=bitcoin"

# Get details of a specific machine
curl -X GET "http://localhost:8000/api/public/machines/1"
```

### Admin API Usage

Admin endpoints require authentication:

```bash
# List all machines (admin)
curl -X GET "http://localhost:8000/api/admin/machines" \
  -H "Authorization: Bearer {admin_token}"

# Create a new machine
curl -X POST "http://localhost:8000/api/admin/machines" \
  -H "Authorization: Bearer {admin_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Bitcoin Miner Pro",
    "description": "High-performance mining machine",
    "price": 1500.00,
    "harvest_time": 120,
    "income_per_harvest": 25.50,
    "total_time": 2880
  }'

# Update a machine
curl -X PUT "http://localhost:8000/api/admin/machines/1" \
  -H "Authorization: Bearer {admin_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "price": 1800.00,
    "is_active": false
  }'
```

## API Features

### ✅ **Implemented Features**

- **RESTful Design**: Follows REST conventions and HTTP standards
- **Comprehensive Validation**: All inputs validated with custom error messages
- **Security**: Admin authentication required for management operations
- **Error Handling**: Graceful error handling with appropriate HTTP status codes
- **Pagination**: Efficient pagination for large datasets
- **Search**: Search functionality for finding specific machines
- **Filtering**: Filter machines by active status (admin only)
- **Separation of Concerns**: Different endpoints for public and admin access

### 🔧 **Technical Details**

- **Framework**: Laravel 10+ with PHP 8+
- **Database**: MariaDB with proper migrations
- **Authentication**: Token-based authentication for admin endpoints
- **Response Format**: Consistent JSON responses
- **Status Codes**: Proper HTTP status codes (200, 201, 404, 422, 500, 503)

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
    "success": true,
    "message": "Operation completed successfully",
    "data": {
        // Response data here
    }
}
```

### Error Response
```json
{
    "success": false,
    "message": "Error description",
    "error": "Additional error details (optional)",
    "errors": {
        "field_name": ["Validation error messages"]
    }
}
```

## Machine Data Model

```json
{
    "id": 1,
    "name": "Bitcoin Miner Pro",
    "description": "High-performance mining machine for Bitcoin",
    "price": 1500.00,
    "type": "machine_miner",
    "is_active": true,
    "harvest_time": 120,
    "income_per_harvest": 25.50,
    "total_time": 2880,
    "created_at": "2025-07-14T04:21:15.000000Z",
    "updated_at": "2025-07-14T04:21:15.000000Z"
}
```

## Field Descriptions

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Unique machine identifier |
| `name` | string | Machine name (max 255 chars) |
| `description` | string | Machine description (max 1000 chars) |
| `price` | number | Machine price in system currency |
| `type` | string | Always "machine_miner" |
| `is_active` | boolean | Active status (admin only) |
| `harvest_time` | integer | Harvest cycle time in minutes |
| `income_per_harvest` | number | Income per harvest cycle |
| `total_time` | integer | Total operational time in minutes |
| `created_at` | string | Creation timestamp (ISO 8601) |
| `updated_at` | string | Last update timestamp (ISO 8601) |

## Testing

### Using cURL

All examples in the documentation use cURL for easy testing. Replace `{admin_token}` with your actual admin authentication token.

### Using Postman

Import the following base configuration:
- **Base URL**: `http://localhost:8000/api`
- **Headers**:
  - `Content-Type: application/json`
  - `Authorization: Bearer {admin_token}` (for admin endpoints)

### Using JavaScript/Fetch

```javascript
// Public endpoint example
fetch('http://localhost:8000/api/public/machines')
  .then(response => response.json())
  .then(data => console.log(data));

// Admin endpoint example
fetch('http://localhost:8000/api/admin/machines', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + adminToken
  },
  body: JSON.stringify({
    name: 'New Machine',
    description: 'Machine description',
    price: 1000,
    harvest_time: 60,
    income_per_harvest: 10,
    total_time: 1440
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

## Environment Setup

Make sure your environment is properly configured:

1. **Database**: MariaDB running with proper migrations
2. **Laravel**: Application running on port 8000
3. **Authentication**: Admin authentication system configured
4. **Permissions**: Proper file permissions for Laravel

## Support

For questions or issues with the API:

1. Check the detailed documentation in [machine-api.md](machine-api.md)
2. Verify your request format matches the examples
3. Ensure proper authentication for admin endpoints
4. Check HTTP status codes for error diagnosis

---

**Documentation Version**: 1.0
**Last Updated**: July 14, 2025
**API Version**: 1.0
