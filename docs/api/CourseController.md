# Course Management API

## Base URL
```
https://your-api-domain.com/api
```

## Authentication
Admin endpoints require authentication. Include the admin token in the Authorization header:
```bash
Authorization: Bearer YOUR_ADMIN_TOKEN
```

---

## List Courses
**Admin:** `GET /admin/courses`  
**Public:** `GET /public/courses`

```bash
# Admin - List all courses with pagination
curl -X GET "https://your-api-domain.com/api/admin/courses?per_page=10" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Public - Browse courses
curl -X GET "https://your-api-domain.com/api/public/courses"
```

**Response:**
```json
{
    "success": true,
    "message": "Courses retrieved successfully",
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 1,
                "title": "English Basics",
                "description": "Learn fundamental English skills",
                "created_at": "2025-07-20T10:30:00.000000Z",
                "updated_at": "2025-07-20T10:30:00.000000Z",
                "units": [
                    {
                        "id": 1,
                        "title": "Unit 1: Greetings",
                        "skill_type": "vocabulary",
                        "difficulty": "beginner"
                    }
                ]
            }
        ],
        "per_page": 15,
        "total": 1
    }
}
```

---

## Create Course
**Endpoint:** `POST /admin/courses`

```bash
curl -X POST "https://your-api-domain.com/api/admin/courses" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Advanced English",
    "description": "Master advanced English concepts"
  }'
```

**Request Body:**
```json
{
    "title": "Advanced English",          // Required, max 255 chars, unique
    "description": "Course description"   // Optional, max 2000 chars
}
```

**Response:**
```json
{
    "success": true,
    "message": "Course created successfully",
    "data": {
        "id": 2,
        "title": "Advanced English",
        "description": "Master advanced English concepts",
        "created_at": "2025-07-20T11:00:00.000000Z",
        "updated_at": "2025-07-20T11:00:00.000000Z",
        "units": []
    }
}
```

**Validation Error:**
```json
{
    "success": false,
    "message": "The given data was invalid.",
    "errors": {
        "title": ["The title field is required."]
    }
}
```

**Duplicate Title Error:**
```json
{
    "success": false,
    "message": "The given data was invalid.",
    "errors": {
        "title": ["A course with this title already exists"]
    }
}
```

---

## Get Course Details
**Admin:** `GET /admin/courses/{id}`  
**Public:** `GET /public/courses/{id}`

```bash
# Admin - Full course details with units and assessments
curl -X GET "https://your-api-domain.com/api/admin/courses/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Public - Course details for public viewing
curl -X GET "https://your-api-domain.com/api/public/courses/1"
```

**Response:**
```json
{
    "success": true,
    "message": "Course retrieved successfully",
    "data": {
        "id": 1,
        "title": "English Basics",
        "description": "Learn fundamental English skills",
        "created_at": "2025-07-20T10:30:00.000000Z",
        "updated_at": "2025-07-20T10:30:00.000000Z",
        "units": [
            {
                "id": 1,
                "title": "Unit 1: Greetings",
                "description": "Learn basic greeting phrases",
                "skill_type": "vocabulary",
                "difficulty": "beginner",
                "unit_order": 1,
                "assessments": [
                    {
                        "id": 1,
                        "question": "What is 'hello' in English?",
                        "pivot": {
                            "assessment_order": 1
                        }
                    }
                ]
            }
        ]
    }
}
```

**Not Found Error:**
```json
{
    "success": false,
    "message": "Resource not found"
}
```

---

## Update Course
**Endpoint:** `PUT/PATCH /admin/courses/{id}`

```bash
# Full update
curl -X PUT "https://your-api-domain.com/api/admin/courses/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "English Fundamentals",
    "description": "Updated comprehensive English course"
  }'

# Partial update
curl -X PATCH "https://your-api-domain.com/api/admin/courses/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "description": "Updated description only"
  }'
```

**Request Body:**
```json
{
    "title": "English Fundamentals",      // Optional, max 255 chars, unique
    "description": "Updated description"  // Optional, max 2000 chars
}
```

**Response:**
```json
{
    "success": true,
    "message": "Course updated successfully",
    "data": {
        "id": 1,
        "title": "English Fundamentals",
        "description": "Updated comprehensive English course",
        "created_at": "2025-07-20T10:30:00.000000Z",
        "updated_at": "2025-07-20T11:15:00.000000Z",
        "units": [
            {
                "id": 1,
                "title": "Unit 1: Greetings"
            }
        ]
    }
}
```

---

## Delete Course
**Endpoint:** `DELETE /admin/courses/{id}`

```bash
curl -X DELETE "https://your-api-domain.com/api/admin/courses/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

**Success Response:**
```json
{
    "success": true,
    "message": "Course deleted successfully",
    "data": null
}
```

**Error (Course has units):**
```json
{
    "success": false,
    "message": "Failed to delete course",
    "error": "Cannot delete course that contains units. Please remove or move units first."
}
```

**Not Found Error:**
```json
{
    "success": false,
    "message": "Resource not found"
}
```

---

## Duplicate Course
**Endpoint:** `POST /admin/courses/{id}/duplicate`

Duplicates a course with all its units and assessments.

```bash
curl -X POST "https://your-api-domain.com/api/admin/courses/1/duplicate" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

**Response:**
```json
{
    "success": true,
    "message": "Course duplicated successfully",
    "data": {
        "id": 3,
        "title": "English Basics (Copy)",
        "description": "Learn fundamental English skills",
        "created_at": "2025-07-20T11:30:00.000000Z",
        "updated_at": "2025-07-20T11:30:00.000000Z",
        "units": [
            {
                "id": 5,
                "title": "Unit 1: Greetings",
                "description": "Learn basic greeting phrases",
                "skill_type": "vocabulary",
                "difficulty": "beginner",
                "unit_order": 1,
                "assessments": [
                    {
                        "id": 1,
                        "question": "What is 'hello' in English?",
                        "pivot": {
                            "assessment_order": 1
                        }
                    }
                ]
            }
        ]
    }
}
```

**Title Conflict Handling:**
- Original: "English Basics" → Copy: "English Basics (Copy)"
- If "English Basics (Copy)" exists → "English Basics (Copy 2)"
- Continues numbering until unique title found

---

## Course Statistics
**Endpoint:** `GET /admin/courses-with-stats`

Get courses with unit count and assessment statistics.

```bash
curl -X GET "https://your-api-domain.com/api/admin/courses-with-stats" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

**Response:**
```json
{
    "success": true,
    "message": "Courses with statistics retrieved successfully",
    "data": [
        {
            "id": 1,
            "title": "English Basics",
            "description": "Learn fundamental English skills",
            "units_count": 3,
            "total_assessments": 12,
            "created_at": "2025-07-20T10:30:00.000000Z",
            "updated_at": "2025-07-20T10:30:00.000000Z",
            "units": [
                {
                    "id": 1,
                    "course_id": 1,
                    "title": "Unit 1: Greetings",
                    "skill_type": "vocabulary",
                    "difficulty": "beginner"
                },
                {
                    "id": 2,
                    "course_id": 1,
                    "title": "Unit 2: Numbers",
                    "skill_type": "vocabulary",
                    "difficulty": "beginner"
                }
            ]
        }
    ]
}
```

---

## Common Error Responses

### Authentication Error
```json
{
    "success": false,
    "message": "Unauthenticated"
}
```

### Server Error
```json
{
    "success": false,
    "message": "Failed to create course",
    "error": "Database connection error"
}
```

### Rate Limiting
```json
{
    "success": false,
    "message": "Too many requests. Please try again later."
}
```

---

## HTTP Status Codes

- `200` - Success (GET, PUT, PATCH)
- `201` - Created successfully (POST, duplicate)
- `400` - Bad request
- `401` - Unauthorized / Authentication required
- `403` - Forbidden / Insufficient permissions
- `404` - Resource not found
- `422` - Validation failed
- `429` - Rate limit exceeded
- `500` - Internal server error