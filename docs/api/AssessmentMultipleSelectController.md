# Assessment Multiple Select API

## Base URL
```
https://your-api-domain.com/api
```

## Authentication
Admin endpoints require authentication. Include the admin token in the Authorization header:
```bash
Authorization: Bearer YOUR_ADMIN_TOKEN
```

---

## List Assessments
**Admin:** `GET /admin/assessments/multiple-select`

```bash
curl -X GET "https://your-api-domain.com/api/admin/assessments/multiple-select?per_page=20" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

**Query Parameters:**
- `per_page` - Items per page (default: 15, max: 100)

**Response:**
```json
{
    "success": true,
    "message": "Assessments retrieved successfully",
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 1,
                "question": "What is 'hello' in English?",
                "answer_list": ["Hello", "Goodbye", "Please", "Thanks"],
                "correct_answer_indexes": [0],
                "explanations": ["Common greeting used daily"],
                "created_at": "2025-07-20T10:30:00.000000Z",
                "updated_at": "2025-07-20T10:30:00.000000Z",
                "assessment": {
                    "id": 1,
                    "itemable_type": "App\\Models\\AssessmentMultipleSelect",
                    "itemable_id": 1,
                    "units": [
                        {
                            "id": 1,
                            "title": "Unit 1: Greetings",
                            "course_id": 1,
                            "pivot": {
                                "assessment_order": 1
                            }
                        }
                    ]
                }
            }
        ],
        "per_page": 15,
        "total": 1
    }
}
```

---

## Create Assessment
**Endpoint:** `POST /admin/assessments/multiple-select`

```bash
curl -X POST "https://your-api-domain.com/api/admin/assessments/multiple-select" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "How do you say goodbye in English?",
    "answer_list": ["Hello", "Goodbye", "Please", "Thanks"],
    "correct_answer_indexes": [1],
    "explanations": ["Used when leaving or ending a conversation"],
    "unit_attachments": [
        {
            "unit_id": 1,
            "assessment_order": 2
        }
    ]
  }'
```

**Request Body:**
```json
{
    "question": "How do you say goodbye in English?",      // Required, max 1000 chars
    "answer_list": ["Hello", "Goodbye", "Please", "Thanks"], // Required, array of 2-4 options, max 500 chars each
    "correct_answer_indexes": [1],                        // Required, array of correct answer indexes (0-based)
    "explanations": ["Used when leaving"],                // Optional, array of explanations, max 1000 chars each
    "unit_attachments": [                                  // Optional, attach to units during creation
        {
            "unit_id": 1,                                  // Required if provided, must exist in units table
            "assessment_order": 2                          // Required if provided, order within unit (min: 1)
        }
    ]
}
```

**Response:**
```json
{
    "success": true,
    "message": "Assessment created successfully",
    "data": {
        "id": 2,
        "question": "How do you say goodbye in English?",
        "answer_list": ["Hello", "Goodbye", "Please", "Thanks"],
        "correct_answer_indexes": [1],
        "explanations": ["Used when leaving or ending a conversation"],
        "created_at": "2025-07-20T11:30:00.000000Z",
        "updated_at": "2025-07-20T11:30:00.000000Z",
        "assessment": {
            "id": 2,
            "itemable_type": "App\\Models\\AssessmentMultipleSelect",
            "itemable_id": 2,
            "units": [
                {
                    "id": 1,
                    "title": "Unit 1: Greetings",
                    "pivot": {
                        "assessment_order": 2
                    }
                }
            ]
        }
    }
}
```

**Validation Error:**
```json
{
    "success": false,
    "message": "The given data was invalid.",
    "errors": {
        "question": ["The question field is required."],
        "answer_list": ["The answer list must contain at least 2 options."],
        "correct_answer_indexes": ["The correct answer indexes field is required."]
    }
}
```

---

## Get Assessment Details
**Admin:** `GET /admin/assessments/multiple-select/{id}`  
**Public:** `GET /public/assessments/multiple-select/{id}`

```bash
# Admin - Full assessment details
curl -X GET "https://your-api-domain.com/api/admin/assessments/multiple-select/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Public - Assessment for quiz taking (answers may be shuffled)
curl -X GET "https://your-api-domain.com/api/public/assessments/multiple-select/1"
```

**Response:**
```json
{
    "success": true,
    "message": "Assessment retrieved successfully",
    "data": {
        "id": 1,
        "question": "What is 'hello' in English?",
        "answer_list": ["Hello", "Goodbye", "Please", "Thanks"],
        "correct_answer_indexes": [0],
        "explanations": ["Common greeting used daily"],
        "created_at": "2025-07-20T10:30:00.000000Z",
        "updated_at": "2025-07-20T10:30:00.000000Z",
        "assessment": {
            "id": 1,
            "itemable_type": "App\\Models\\AssessmentMultipleSelect",
            "itemable_id": 1,
            "units": [
                {
                    "id": 1,
                    "title": "Unit 1: Greetings",
                    "course_id": 1,
                    "course": {
                        "id": 1,
                        "title": "English Basics"
                    },
                    "pivot": {
                        "assessment_order": 1
                    }
                }
            ]
        }
    }
}
```

---

## Update Assessment
**Endpoint:** `PUT/PATCH /admin/assessments/multiple-select/{id}`

```bash
# Full update
curl -X PUT "https://your-api-domain.com/api/admin/assessments/multiple-select/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "What is the correct greeting in English?",
    "answer_list": ["Hello", "Goodbye", "Please", "Thanks"],
    "correct_answer_indexes": [0],
    "explanations": ["Most common greeting used daily"]
  }'

# Partial update
curl -X PATCH "https://your-api-domain.com/api/admin/assessments/multiple-select/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "What is the correct greeting in English?",
    "explanations": ["Most common greeting used daily"]
  }'
```

**Request Body (all fields optional for PATCH):**
```json
{
    "question": "What is the correct greeting in English?", // Optional, max 1000 chars
    "answer_list": ["Hello", "Goodbye", "Please", "Thanks"], // Optional, array of 2-4 options
    "correct_answer_indexes": [0],                          // Optional, array of correct indexes
    "explanations": ["Most common greeting used daily"]     // Optional, array of explanations
}
```

**Response:**
```json
{
    "success": true,
    "message": "Assessment updated successfully",
    "data": {
        "id": 1,
        "question": "What is the correct greeting in English?",
        "answer_list": ["Hello", "Goodbye", "Please", "Thanks"],
        "correct_answer_indexes": [0],
        "explanations": ["Most common greeting used daily"],
        "created_at": "2025-07-20T10:30:00.000000Z",
        "updated_at": "2025-07-20T12:00:00.000000Z"
    }
}
```

---

## Delete Assessment
**Endpoint:** `DELETE /admin/assessments/multiple-select/{id}`

```bash
curl -X DELETE "https://your-api-domain.com/api/admin/assessments/multiple-select/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

**Success Response:**
```json
{
    "success": true,
    "message": "Assessment deleted successfully",
    "data": null
}
```

**Error Response:**
```json
{
    "success": false,
    "message": "Failed to delete assessment",
    "error": "Assessment is still attached to units"
}
```

---

## Attach Assessment to Units
**Endpoint:** `POST /admin/assessments/multiple-select/{id}/attach-units`

Attaches an assessment to one or more units with specified order positions.

```bash
curl -X POST "https://your-api-domain.com/api/admin/assessments/multiple-select/1/attach-units" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "unit_attachments": [
        {
            "unit_id": 2,
            "assessment_order": 1
        },
        {
            "unit_id": 3,
            "assessment_order": 3
        }
    ]
  }'
```

**Request Body:**
```json
{
    "unit_attachments": [                    // Required, array of unit attachments
        {
            "unit_id": 2,                    // Required, must exist in units table
            "assessment_order": 1            // Required, order within unit (min: 1)
        },
        {
            "unit_id": 3,
            "assessment_order": 3
        }
    ]
}
```

**Response:**
```json
{
    "success": true,
    "message": "Assessment attached to units successfully",
    "data": null
}
```

**Validation Error:**
```json
{
    "success": false,
    "message": "The given data was invalid.",
    "errors": {
        "unit_attachments.0.unit_id": ["The selected unit id is invalid."],
        "unit_attachments.1.assessment_order": ["The assessment order must be at least 1."]
    }
}
```

**Already Attached Error:**
```json
{
    "success": false,
    "message": "Failed to attach assessment to units",
    "error": "Assessment is already attached to unit 2"
}
```

---

## Detach Assessment from Units
**Endpoint:** `POST /admin/assessments/multiple-select/{id}/detach-units`

Detaches an assessment from specific units or all units.

```bash
# Detach from specific units
curl -X POST "https://your-api-domain.com/api/admin/assessments/multiple-select/1/detach-units" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "unit_ids": [2, 3]
  }'

# Detach from all units (empty array or omit unit_ids)
curl -X POST "https://your-api-domain.com/api/admin/assessments/multiple-select/1/detach-units" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "unit_ids": []
  }'

# Detach from all units (no body)
curl -X POST "https://your-api-domain.com/api/admin/assessments/multiple-select/1/detach-units" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}'
```

**Request Body:**
```json
{
    "unit_ids": [2, 3]  // Optional, array of unit IDs to detach from. Empty array or omit to detach from all units
}
```

**Response:**
```json
{
    "success": true,
    "message": "Assessment detached from units successfully",
    "data": null
}
```

**Validation Error:**
```json
{
    "success": false,
    "message": "The given data was invalid.",
    "errors": {
        "unit_ids.0": ["The selected unit id is invalid."]
    }
}
```

---

## Assessment Data Structure

### JSON Format
All assessment data is stored in JSON format with the following structure:

```json
{
    "question": "What is 'hello' in English?",
    "answer_list": ["Hello", "Goodbye", "Please", "Thanks"],
    "correct_answer_indexes": [0],
    "explanations": ["Common greeting used daily"]
}
```

### Answer List Requirements
- **Minimum:** 2 options
- **Maximum:** 4 options  
- **Length:** Each option max 500 characters
- **Type:** Array of strings

### Correct Answer Indexes
- **Format:** Array of integers (0-based indexing)
- **Multiple correct answers:** Supported (e.g., `[0, 2]` for options 1 and 3)
- **Validation:** Must correspond to valid indexes in answer_list

### Explanations
- **Format:** Array of strings
- **Length:** Each explanation max 1000 characters
- **Usage:** Displayed after answer submission to explain correct answers

### Helpful Model Methods
```php
// Get array of correct answer texts
$assessment->correct_answers; // ["Hello"]

// Check if an index is correct
$assessment->isCorrectAnswer(0); // true

// Get shuffled answers (for public quiz display)
$assessment->shuffled_answers; // Always returns shuffled array
```

---

## Common Error Responses

### Authentication Error
```json
{
    "success": false,
    "message": "Unauthenticated"
}
```

### Validation Error
```json
{
    "success": false,
    "message": "The given data was invalid.",
    "errors": {
        "question": ["The question field is required."],
        "answer_list": ["The answer list must contain at least 2 options."],
        "correct_answer_indexes": ["The correct answer indexes field is required."]
    }
}
```

### Not Found Error
```json
{
    "success": false,
    "message": "Resource not found"
}
```

### Server Error
```json
{
    "success": false,
    "message": "Failed to create assessment",
    "error": "Database connection error"
}
```

---

## HTTP Status Codes

- `200` - Success (GET, PUT, PATCH)
- `201` - Created successfully (POST)
- `400` - Bad request
- `401` - Unauthorized / Authentication required
- `403` - Forbidden / Insufficient permissions
- `404` - Resource not found
- `422` - Validation failed
- `429` - Rate limit exceeded
- `500` - Internal server error