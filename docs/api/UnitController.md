# Unit Management API

## Base URL
```
https://your-api-domain.com/api
```

## Authentication
Admin endpoints require authentication. Include the admin token in the Authorization header:
```bash
Authorization: Bearer YOUR_ADMIN_TOKEN
```

---

## List Units
**Admin:** `GET /admin/units`  
**Public:** `GET /public/courses/{courseId}/units`

```bash
# Admin - List all units with filters
curl -X GET "https://your-api-domain.com/api/admin/units?course_id=1&skill_type=vocabulary&difficulty=beginner&per_page=20" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Public - Get units by course
curl -X GET "https://your-api-domain.com/api/public/courses/1/units?skill_type=vocabulary&per_page=50"
```

**Query Parameters:**
- `course_id` - Filter by course ID
- `skill_type` - Filter by skill type: `vocabulary`, `grammar`, `listening`, `reading`, `speaking`, `writing`
- `difficulty` - Filter by difficulty: `beginner`, `elementary`, `intermediate`, `upper_intermediate`, `advanced`, `proficient`
- `per_page` - Items per page (default: 15, max: 100)

**Response:**
```json
{
    "success": true,
    "message": "Units retrieved successfully", 
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 1,
                "course_id": 1,
                "title": "Unit 1: Basic Greetings",
                "description": "Learn basic greeting phrases",
                "skill_type": "vocabulary",
                "difficulty": "beginner",
                "unit_order": 1,
                "created_at": "2025-07-20T10:30:00.000000Z",
                "updated_at": "2025-07-20T10:30:00.000000Z",
                "course": {
                    "id": 1,
                    "title": "English Basics",
                    "description": "Learn fundamental English skills"
                },
                "assessments": [
                    {
                        "id": 1,
                        "question": "What is 'hello' in English?",
                        "pivot": {
                            "assessment_order": 1
                        }
                    }
                ]
            }
        ],
        "per_page": 15,
        "total": 1,
        "last_page": 1
    }
}
```

---

## Create Unit
**Endpoint:** `POST /admin/units`

```bash
curl -X POST "https://your-api-domain.com/api/admin/units" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "course_id": 1,
    "title": "Unit 2: Numbers",
    "description": "Learn numbers 1-100", 
    "skill_type": "vocabulary",
    "difficulty": "beginner",
    "unit_order": 2
  }'
```

**Request Body:**
```json
{
    "course_id": 1,                       // Required, must exist in courses table
    "title": "Unit 2: Numbers",           // Required, max 255 chars
    "description": "Learn numbers 1-100", // Optional, max 2000 chars
    "skill_type": "vocabulary",           // Required: vocabulary, grammar, listening, reading, speaking, writing
    "difficulty": "beginner",             // Required: beginner, elementary, intermediate, upper_intermediate, advanced, proficient
    "unit_order": 2                       // Optional, auto-assigned if not provided
}
```

**Response:**
```json
{
    "success": true,
    "message": "Unit created successfully",
    "data": {
        "id": 2,
        "course_id": 1,
        "title": "Unit 2: Numbers",
        "description": "Learn numbers 1-100",
        "skill_type": "vocabulary", 
        "difficulty": "beginner",
        "unit_order": 2,
        "created_at": "2025-07-20T11:15:00.000000Z",
        "updated_at": "2025-07-20T11:15:00.000000Z",
        "course": {
            "id": 1,
            "title": "English Basics"
        },
        "assessments": []
    }
}
```

**Validation Error:**
```json
{
    "success": false,
    "message": "The given data was invalid.",
    "errors": {
        "course_id": ["The selected course id is invalid."],
        "title": ["The title field is required."],
        "skill_type": ["The selected skill type is invalid."]
    }
}
```

---

## Get Unit Details
**Admin:** `GET /admin/units/{id}`  
**Public:** `GET /public/units/{id}`

```bash
# Admin - Full unit details
curl -X GET "https://your-api-domain.com/api/admin/units/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Public - Unit details for public viewing
curl -X GET "https://your-api-domain.com/api/public/units/1"
```

**Response:**
```json
{
    "success": true,
    "message": "Unit retrieved successfully",
    "data": {
        "id": 1,
        "course_id": 1,
        "title": "Unit 1: Basic Greetings",
        "description": "Learn basic greeting phrases",
        "skill_type": "vocabulary",
        "difficulty": "beginner",
        "unit_order": 1,
        "created_at": "2025-07-20T10:30:00.000000Z",
        "updated_at": "2025-07-20T10:30:00.000000Z",
        "course": {
            "id": 1,
            "title": "English Basics",
            "description": "Learn fundamental English skills"
        },
        "assessments": [
            {
                "id": 1,
                "question": "What is 'hello' in English?",
                "answer_list": ["Hello", "Goodbye", "Please", "Thanks"],
                "correct_answer_indexes": [0],
                "pivot": {
                    "assessment_order": 1
                }
            },
            {
                "id": 2,
                "question": "How do you say goodbye?",
                "answer_list": ["Hello", "Goodbye", "Please", "Thanks"],
                "correct_answer_indexes": [1],
                "pivot": {
                    "assessment_order": 2
                }
            }
        ]
    }
}
```

---

## Update Unit
**Endpoint:** `PUT/PATCH /admin/units/{id}`

```bash
# Full update
curl -X PUT "https://your-api-domain.com/api/admin/units/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "course_id": 1,
    "title": "Unit 1: Essential Greetings",
    "description": "Updated description",
    "skill_type": "vocabulary",
    "difficulty": "elementary",
    "unit_order": 1
  }'

# Partial update
curl -X PATCH "https://your-api-domain.com/api/admin/units/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Unit 1: Essential Greetings",
    "difficulty": "elementary"
  }'
```

**Request Body (all fields optional for PATCH):**
```json
{
    "course_id": 1,                       // Optional, must exist in courses table
    "title": "Unit 1: Essential Greetings", // Optional, max 255 chars
    "description": "Updated description", // Optional, max 2000 chars
    "skill_type": "vocabulary",           // Optional: vocabulary, grammar, listening, reading, speaking, writing
    "difficulty": "elementary",           // Optional: beginner, elementary, intermediate, upper_intermediate, advanced, proficient
    "unit_order": 1                       // Optional
}
```

**Response:**
```json
{
    "success": true,
    "message": "Unit updated successfully",
    "data": {
        "id": 1,
        "course_id": 1,
        "title": "Unit 1: Essential Greetings",
        "description": "Updated description",
        "skill_type": "vocabulary",
        "difficulty": "elementary",
        "unit_order": 1,
        "created_at": "2025-07-20T10:30:00.000000Z",
        "updated_at": "2025-07-20T11:45:00.000000Z",
        "course": {
            "id": 1,
            "title": "English Basics"
        },
        "assessments": []
    }
}
```

---

## Delete Unit
**Endpoint:** `DELETE /admin/units/{id}`

```bash
curl -X DELETE "https://your-api-domain.com/api/admin/units/1" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

**Success Response:**
```json
{
    "success": true,
    "message": "Unit deleted successfully",
    "data": null
}
```

**Error Response:**
```json
{
    "success": false,
    "message": "Failed to delete unit",
    "error": "Cannot delete unit with active assessments"
}
```

---

## Duplicate Unit
**Endpoint:** `POST /admin/units/{id}/duplicate`

Creates a copy of the unit within the same course with all assessments.

```bash
curl -X POST "https://your-api-domain.com/api/admin/units/1/duplicate" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "target_order": 3
  }'
```

**Request Body:**
```json
{
    "target_order": 3  // Optional, position for duplicated unit
}
```

**Response:**
```json
{
    "success": true,
    "message": "Unit duplicated successfully",
    "data": {
        "id": 5,
        "course_id": 1,
        "title": "Unit 1: Basic Greetings (Copy)",
        "description": "Learn basic greeting phrases",
        "skill_type": "vocabulary",
        "difficulty": "beginner",
        "unit_order": 3,
        "created_at": "2025-07-20T12:00:00.000000Z",
        "updated_at": "2025-07-20T12:00:00.000000Z",
        "course": {
            "id": 1,
            "title": "English Basics"
        },
        "assessments": [
            {
                "id": 1,
                "question": "What is 'hello' in English?",
                "pivot": {
                    "assessment_order": 1
                }
            }
        ]
    }
}
```

---

## Move Unit to Different Course
**Endpoint:** `PATCH /admin/units/{id}/move-to-course`

Moves a unit from one course to another with proper reordering.

```bash
curl -X PATCH "https://your-api-domain.com/api/admin/units/1/move-to-course" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "target_course_id": 2,
    "target_order": 1
  }'
```

**Request Body:**
```json
{
    "target_course_id": 2,  // Required, must exist in courses table
    "target_order": 1       // Optional, position in target course
}
```

**Response:**
```json
{
    "success": true,
    "message": "Unit moved to course successfully",
    "data": {
        "id": 1,
        "course_id": 2,
        "title": "Unit 1: Basic Greetings",
        "description": "Learn basic greeting phrases",
        "skill_type": "vocabulary",
        "difficulty": "beginner",
        "unit_order": 1,
        "created_at": "2025-07-20T10:30:00.000000Z",
        "updated_at": "2025-07-20T12:15:00.000000Z",
        "course": {
            "id": 2,
            "title": "Advanced English"
        },
        "assessments": []
    }
}
```

---

## Reorder Unit
**Endpoint:** `PATCH /admin/units/{id}/reorder`

Changes the order position of a unit within its course.

```bash
curl -X PATCH "https://your-api-domain.com/api/admin/units/1/reorder" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "new_order": 5,
    "course_id": 1
  }'
```

**Request Body:**
```json
{
    "new_order": 5,     // Required, new position (min: 1)
    "course_id": 1      // Optional, defaults to unit's current course
}
```

**Response:**
```json
{
    "success": true,
    "message": "Unit reordered successfully",
    "data": {
        "id": 1,
        "course_id": 1,
        "title": "Unit 1: Basic Greetings",
        "description": "Learn basic greeting phrases",
        "skill_type": "vocabulary",
        "difficulty": "beginner",
        "unit_order": 5,
        "created_at": "2025-07-20T10:30:00.000000Z",
        "updated_at": "2025-07-20T12:30:00.000000Z",
        "course": {
            "id": 1,
            "title": "English Basics"
        },
        "assessments": []
    }
}
```

---

## Get Units by Course
**Admin:** `GET /admin/courses/{courseId}/units`  
**Public:** `GET /public/courses/{courseId}/units`

Retrieves all units for a specific course with optional filtering.

```bash
# Admin - Course units with full details
curl -X GET "https://your-api-domain.com/api/admin/courses/1/units?skill_type=vocabulary&difficulty=beginner&per_page=50" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Public - Course units for public viewing
curl -X GET "https://your-api-domain.com/api/public/courses/1/units?per_page=50"
```

**Query Parameters:**
- `skill_type` - Filter by skill type
- `difficulty` - Filter by difficulty level
- `per_page` - Items per page (default: 50, max: 100)

**Response:**
```json
{
    "success": true,
    "message": "Course units retrieved successfully",
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 1,
                "course_id": 1,
                "title": "Unit 1: Basic Greetings",
                "description": "Learn basic greeting phrases",
                "skill_type": "vocabulary",
                "difficulty": "beginner",
                "unit_order": 1,
                "assessments": [
                    {
                        "id": 1,
                        "question": "What is 'hello' in English?",
                        "pivot": {
                            "assessment_order": 1
                        }
                    }
                ]
            },
            {
                "id": 2,
                "course_id": 1,
                "title": "Unit 2: Numbers",
                "description": "Learn numbers 1-100",
                "skill_type": "vocabulary",
                "difficulty": "beginner",
                "unit_order": 2,
                "assessments": []
            }
        ],
        "per_page": 50,
        "total": 2
    }
}
```

**Course Not Found:**
```json
{
    "success": true,
    "message": "Course units retrieved successfully",
    "data": {
        "current_page": 1,
        "data": [],
        "per_page": 50,
        "total": 0
    }
}
```

---

## Common Error Responses

### Authentication Error
```json
{
    "success": false,
    "message": "Unauthenticated"
}
```

### Validation Error
```json
{
    "success": false,
    "message": "The given data was invalid.",
    "errors": {
        "course_id": ["The selected course id is invalid."],
        "skill_type": ["The selected skill type is invalid."],
        "difficulty": ["The selected difficulty is invalid."]
    }
}
```

### Not Found Error
```json
{
    "success": false,
    "message": "Resource not found"
}
```

### Server Error
```json
{
    "success": false,
    "message": "Failed to create unit",
    "error": "Database constraint violation"
}
```

---

## Enum Values

### Skill Types
- `vocabulary` - Vocabulary learning
- `grammar` - Grammar rules and structure
- `listening` - Listening comprehension
- `reading` - Reading comprehension
- `speaking` - Speaking practice
- `writing` - Writing skills

### Difficulty Levels
- `beginner` - Beginner level
- `elementary` - Elementary level
- `intermediate` - Intermediate level
- `upper_intermediate` - Upper intermediate level
- `advanced` - Advanced level
- `proficient` - Proficient level

---

## HTTP Status Codes

- `200` - Success (GET, PUT, PATCH)
- `201` - Created successfully (POST, duplicate)
- `400` - Bad request
- `401` - Unauthorized / Authentication required
- `403` - Forbidden / Insufficient permissions
- `404` - Resource not found
- `422` - Validation failed
- `429` - Rate limit exceeded
- `500` - Internal server error