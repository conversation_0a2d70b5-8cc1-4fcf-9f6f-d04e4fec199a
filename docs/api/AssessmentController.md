# Assessment API Documentation

## Overview

The Assessment API has been refactored to use a unified controller that handles both Multiple Select and AI Gap Fill Sentence assessments through a strategy pattern. This replaces the previous separate controllers for each assessment type.

## Base URL

All assessment endpoints are under the `/api/admin/assessments` prefix and require admin authentication.

## Authentication

All endpoints require admin authentication via the `admin.auth` middleware.

## Assessment Types

The API supports two assessment types:
- `multiple-select` - Multiple choice questions with selectable answers
- `ai-gap-fill-sentence` - AI-powered gap fill questions with context

## Endpoints

### Create Assessment

**Endpoint**: `POST /api/admin/assessments`  
**Route Name**: `admin.assessments.store`

Creates a new assessment based on the specified type.

#### Request Body

**Common Fields**:
```json
{
  "type": "multiple-select|ai-gap-fill-sentence"
}
```

**Multiple Select Assessment**:
```json
{
  "type": "multiple-select",
  "question": "What is the capital of France?",
  "answer_list": [
    "London",
    "Berlin", 
    "Paris",
    "Madrid"
  ],
  "correct_answer_indexes": [2],
  "explanations": [
    "Paris is the capital and largest city of France."
  ],
  "unit_attachments": [
    {
      "unit_id": 1,
      "assessment_order": 1
    }
  ]
}
```

**AI Gap Fill Sentence Assessment**:
```json
{
  "type": "ai-gap-fill-sentence",
  "question": "The quick brown fox jumps over the [blank] dog.",
  "context": "This is a famous English pangram used to test typing skills.",
  "unit_attachments": [
    {
      "unit_id": 1,
      "assessment_order": 2
    }
  ]
}
```

#### Validation Rules

**Multiple Select**:
- `type`: required|string|in:multiple-select,ai-gap-fill-sentence
- `question`: required|string|max:1000
- `answer_list`: required|array|min:2|max:10
- `answer_list.*`: required|string|max:500
- `correct_answer_indexes`: required|array|min:1
- `correct_answer_indexes.*`: required|integer|min:0
- `explanations`: nullable|array
- `explanations.*`: nullable|string|max:1000
- `unit_attachments`: nullable|array
- `unit_attachments.*.unit_id`: required|integer|exists:units,id
- `unit_attachments.*.assessment_order`: nullable|integer|min:1

**AI Gap Fill Sentence**:
- `type`: required|string|in:multiple-select,ai-gap-fill-sentence
- `question`: required|string|max:1000
- `context`: required|string|max:2000
- `unit_attachments`: nullable|array
- `unit_attachments.*.unit_id`: required|integer|exists:units,id
- `unit_attachments.*.assessment_order`: nullable|integer|min:1

#### Response

**Success (201)**:
```json
{
  "success": true,
  "message": "Assessment created successfully",
  "data": {
    "id": 1,
    "question": "What is the capital of France?",
    "answer_list": ["London", "Berlin", "Paris", "Madrid"],
    "correct_answer_indexes": [2],
    "explanations": ["Paris is the capital and largest city of France."],
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T00:00:00.000000Z",
    "assessment": {
      "id": 1,
      "itemable_type": "App\\Models\\AssessmentMultipleSelect",
      "itemable_id": 1,
      "units": [...]
    }
  }
}
```

### Update Assessment

**Endpoint**: `PUT /api/admin/assessments/{type}/{id}`  
**Route Name**: `admin.assessments.update`

Updates an existing assessment by type and ID.

#### URL Parameters

- `type`: Assessment type (multiple-select|ai-gap-fill-sentence)
- `id`: Assessment ID (integer)

#### Request Body

**Multiple Select Assessment**:
```json
{
  "question": "Updated question text?",
  "answer_list": [
    "Option 1",
    "Option 2",
    "Option 3"
  ],
  "correct_answer_indexes": [1],
  "explanations": ["Updated explanation"]
}
```

**AI Gap Fill Sentence Assessment**:
```json
{
  "question": "Updated question with [blank].",
  "context": "Updated context for the question."
}
```

#### Validation Rules

**Multiple Select**:
- `question`: sometimes|required|string|max:1000
- `answer_list`: sometimes|required|array|min:2|max:10
- `answer_list.*`: required|string|max:500
- `correct_answer_indexes`: sometimes|required|array|min:1
- `correct_answer_indexes.*`: required|integer|min:0
- `explanations`: nullable|array
- `explanations.*`: nullable|string|max:1000

**AI Gap Fill Sentence**:
- `question`: sometimes|required|string|max:1000
- `context`: sometimes|required|string|max:2000

#### Response

**Success (200)**:
```json
{
  "success": true,
  "message": "Assessment updated successfully",
  "data": {
    "id": 1,
    "question": "Updated question text?",
    "answer_list": ["Option 1", "Option 2", "Option 3"],
    "correct_answer_indexes": [1],
    "explanations": ["Updated explanation"],
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T01:00:00.000000Z"
  }
}
```

### Delete Assessment

**Endpoint**: `DELETE /api/admin/assessments/{type}/{id}`  
**Route Name**: `admin.assessments.destroy`

Deletes an assessment and its polymorphic Assessment record.

#### URL Parameters

- `type`: Assessment type (multiple-select|ai-gap-fill-sentence)
- `id`: Assessment ID (integer)

#### Response

**Success (200)**:
```json
{
  "success": true,
  "message": "Assessment deleted successfully",
  "data": null
}
```

### Attach Assessment to Units

**Endpoint**: `POST /api/admin/assessments/{type}/{id}/attach-units`  
**Route Name**: `admin.assessments.attach-units`

Attaches an assessment to one or more units with optional ordering.

#### URL Parameters

- `type`: Assessment type (multiple-select|ai-gap-fill-sentence)
- `id`: Assessment ID (integer)

#### Request Body

```json
{
  "unit_attachments": [
    {
      "unit_id": 1,
      "assessment_order": 1
    },
    {
      "unit_id": 2,
      "assessment_order": 3
    }
  ]
}
```

#### Validation Rules

- `unit_attachments`: required|array
- `unit_attachments.*.unit_id`: required|integer|exists:units,id
- `unit_attachments.*.assessment_order`: nullable|integer|min:1

#### Response

**Success (200)**:
```json
{
  "success": true,
  "message": "Assessment attached to units successfully"
}
```

### Detach Assessment from Units

**Endpoint**: `POST /api/admin/assessments/{type}/{id}/detach-units`  
**Route Name**: `admin.assessments.detach-units`

Detaches an assessment from specified units, or all units if none specified.

#### URL Parameters

- `type`: Assessment type (multiple-select|ai-gap-fill-sentence)
- `id`: Assessment ID (integer)

#### Request Body

```json
{
  "unit_ids": [1, 2, 3]
}
```

**Note**: If `unit_ids` is not provided or empty, the assessment will be detached from all units.

#### Validation Rules

- `unit_ids`: nullable|array
- `unit_ids.*`: integer|exists:units,id

#### Response

**Success (200)**:
```json
{
  "success": true,
  "message": "Assessment detached from units successfully"
}
```

## Error Responses

### Validation Errors (422)

```json
{
  "message": "The given data was invalid.",
  "errors": {
    "question": ["The question field is required."],
    "answer_list": ["The answer list must contain at least 2 items."]
  }
}
```

### Invalid Assessment Type (400)

```json
{
  "success": false,
  "message": "Invalid assessment type"
}
```

### Assessment Not Found (404)

```json
{
  "message": "No query results for model [App\\Models\\AssessmentMultipleSelect] 999"
}
```

### Server Error (500)

```json
{
  "success": false,
  "message": "Failed to create assessment",
  "error": "Detailed error message"
}
```

## Migration from Old API

### Before (Separate Controllers)

```bash
# Multiple Select
POST /api/admin/assessments/multiple-select
PUT /api/admin/assessments/multiple-select/{id}
DELETE /api/admin/assessments/multiple-select/{id}
POST /api/admin/assessments/multiple-select/{id}/attach-units
POST /api/admin/assessments/multiple-select/{id}/detach-units

# AI Gap Fill Sentence
POST /api/admin/assessments/ai-gap-fill-sentence
PUT /api/admin/assessments/ai-gap-fill-sentence/{id}
DELETE /api/admin/assessments/ai-gap-fill-sentence/{id}
POST /api/admin/assessments/ai-gap-fill-sentence/{id}/attach-units
POST /api/admin/assessments/ai-gap-fill-sentence/{id}/detach-units
```

### After (Unified Controller)

```bash
# Unified for all types
POST /api/admin/assessments                     # type in request body
PUT /api/admin/assessments/{type}/{id}
DELETE /api/admin/assessments/{type}/{id}
POST /api/admin/assessments/{type}/{id}/attach-units
POST /api/admin/assessments/{type}/{id}/detach-units
```

### Migration Steps

1. **Creating assessments**: Add `type` field to request body
2. **Updating/deleting**: Change URL to include type parameter
3. **Attach/detach**: Change URL to include type parameter
4. **Request/response format**: Remains the same

## Examples

### Create Multiple Select Assessment

```bash
curl -X POST /api/admin/assessments \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {admin_token}" \
  -d '{
    "type": "multiple-select",
    "question": "Which of the following is a programming language?",
    "answer_list": ["HTML", "CSS", "JavaScript", "JSON"],
    "correct_answer_indexes": [2],
    "explanations": ["JavaScript is a programming language used for web development."]
  }'
```

### Update AI Gap Fill Assessment

```bash
curl -X PUT /api/admin/assessments/ai-gap-fill-sentence/123 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {admin_token}" \
  -d '{
    "question": "The cat sat on the [blank].",
    "context": "A simple sentence about a cat."
  }'
```

### Attach Assessment to Units

```bash
curl -X POST /api/admin/assessments/multiple-select/123/attach-units \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {admin_token}" \
  -d '{
    "unit_attachments": [
      {"unit_id": 1, "assessment_order": 1},
      {"unit_id": 2, "assessment_order": 2}
    ]
  }'
```

## Benefits of Unified API

1. **Consistency**: Single API pattern for all assessment types
2. **Extensibility**: Easy to add new assessment types
3. **Maintainability**: Shared validation and error handling logic
4. **Strategy Pattern**: Clean separation of type-specific logic
5. **DRY Principle**: Eliminates code duplication between controllers