# English Game Backend API Documentation

Welcome to the English Game Backend API documentation. This API provides comprehensive functionality for managing courses, units, assessments, and bulk imports for an English learning platform.

## 📚 API Documentation Index

### Core Controllers
- **[Course Management](./CourseController.md)** - CRUD operations for courses with duplication and statistics
- **[Unit Management](./UnitController.md)** - CRUD operations for learning units with advanced features
- **[Assessment Management](./AssessmentMultipleSelectController.md)** - Multiple-choice assessment CRUD with unit attachments
- **[CSV Import](./ImportController.md)** - Bulk import functionality with validation and templates

## 🚀 Quick Start

### Base URL
```
https://your-api-domain.com/api
```

### Authentication
Admin endpoints require Bearer token authentication:
```bash
Authorization: Bearer YOUR_ADMIN_TOKEN
```

Public endpoints are accessible without authentication for browsing courses and taking quizzes.

### Response Format
All API responses follow this standardized format:

**Success Response:**
```json
{
    "success": true,
    "message": "Operation completed successfully",
    "data": { /* response data */ }
}
```

**Error Response:**
```json
{
    "success": false,
    "message": "Error description",
    "error": "Detailed error message"
}
```

## 🎯 Key Features

### Course Management
- ✅ Full CRUD operations
- ✅ Course duplication with units and assessments
- ✅ Statistics and analytics
- ✅ Public browsing for students

### Unit Management  
- ✅ CRUD operations with filtering
- ✅ Unit duplication within courses
- ✅ Cross-course unit migration
- ✅ Flexible ordering and reordering
- ✅ Skill type and difficulty categorization

### Assessment System
- ✅ Multiple-choice question format
- ✅ JSON-based flexible data storage
- ✅ Multiple correct answers support
- ✅ Unit attachment/detachment
- ✅ Public quiz presentation

### Bulk Import
- ✅ CSV file upload with validation
- ✅ Dry-run validation before import
- ✅ Template download for correct formatting
- ✅ Transaction-safe bulk operations

## 📋 Endpoint Overview

### Admin Endpoints (Authentication Required)

**Courses:**
- `GET /admin/courses` - List courses
- `POST /admin/courses` - Create course
- `GET /admin/courses/{id}` - Course details
- `PUT/PATCH /admin/courses/{id}` - Update course
- `DELETE /admin/courses/{id}` - Delete course
- `POST /admin/courses/{id}/duplicate` - Duplicate course
- `GET /admin/courses-with-stats` - Course statistics

**Units:**
- `GET /admin/units` - List units
- `POST /admin/units` - Create unit
- `GET /admin/units/{id}` - Unit details
- `PUT/PATCH /admin/units/{id}` - Update unit
- `DELETE /admin/units/{id}` - Delete unit
- `POST /admin/units/{id}/duplicate` - Duplicate unit
- `PATCH /admin/units/{id}/move-to-course` - Move unit
- `PATCH /admin/units/{id}/reorder` - Reorder unit
- `GET /admin/courses/{courseId}/units` - Units by course

**Assessments:**
- `GET /admin/assessments/multiple-select` - List assessments
- `POST /admin/assessments/multiple-select` - Create assessment
- `GET /admin/assessments/multiple-select/{id}` - Assessment details
- `PUT/PATCH /admin/assessments/multiple-select/{id}` - Update assessment
- `DELETE /admin/assessments/multiple-select/{id}` - Delete assessment
- `POST /admin/assessments/multiple-select/{id}/attach-units` - Attach to units
- `POST /admin/assessments/multiple-select/{id}/detach-units` - Detach from units

**Import:**
- `POST /admin/import/csv` - Import CSV file
- `POST /admin/import/validate-csv` - Validate CSV (dry run)
- `GET /admin/import/template` - Download CSV template

### Public Endpoints (No Authentication)

**Courses:**
- `GET /public/courses` - Browse courses
- `GET /public/courses/{id}` - Course details

**Units:**
- `GET /public/units/{id}` - Unit details
- `GET /public/courses/{courseId}/units` - Units by course

**Assessments:**
- `GET /public/assessments/multiple-select/{id}` - Assessment for quiz

## 🔧 Data Models

### Course
```json
{
    "id": 1,
    "title": "English Basics",
    "description": "Learn fundamental English skills",
    "created_at": "2025-07-20T10:30:00.000000Z",
    "updated_at": "2025-07-20T10:30:00.000000Z"
}
```

### Unit
```json
{
    "id": 1,
    "course_id": 1,
    "title": "Unit 1: Basic Greetings",
    "description": "Learn basic greeting phrases",
    "skill_type": "vocabulary",
    "difficulty": "beginner",
    "unit_order": 1,
    "created_at": "2025-07-20T10:30:00.000000Z",
    "updated_at": "2025-07-20T10:30:00.000000Z"
}
```

### Assessment Multiple Select
```json
{
    "id": 1,
    "question": "What is 'hello' in English?",
    "answer_list": ["Hello", "Goodbye", "Please", "Thanks"],
    "correct_answer_indexes": [0],
    "explanations": ["Common greeting used daily"],
    "created_at": "2025-07-20T10:30:00.000000Z",
    "updated_at": "2025-07-20T10:30:00.000000Z"
}
```

## 🎲 Enum Values

### Skill Types
- `vocabulary` - Vocabulary learning
- `grammar` - Grammar rules and structure
- `listening` - Listening comprehension
- `reading` - Reading comprehension
- `speaking` - Speaking practice
- `writing` - Writing skills

### Difficulty Levels
- `beginner` - Beginner level
- `elementary` - Elementary level
- `intermediate` - Intermediate level
- `upper_intermediate` - Upper intermediate level
- `advanced` - Advanced level
- `proficient` - Proficient level

## 📊 Common Query Parameters

### Pagination
- `per_page` - Items per page (default varies by endpoint, max: 100)
- `page` - Page number (default: 1)

### Filtering
- `course_id` - Filter by course ID
- `skill_type` - Filter by skill type
- `difficulty` - Filter by difficulty level

## ⚠️ Error Handling

### HTTP Status Codes
- `200` - Success (GET, PUT, PATCH)
- `201` - Created successfully (POST)
- `400` - Bad request
- `401` - Unauthorized / Authentication required
- `403` - Forbidden / Insufficient permissions
- `404` - Resource not found
- `422` - Validation failed
- `429` - Rate limit exceeded
- `500` - Internal server error

### Common Error Responses

**Authentication Error:**
```json
{
    "success": false,
    "message": "Unauthenticated"
}
```

**Validation Error:**
```json
{
    "success": false,
    "message": "The given data was invalid.",
    "errors": {
        "field_name": ["The field is required."]
    }
}
```

**Not Found Error:**
```json
{
    "success": false,
    "message": "Resource not found"
}
```

## 🛠️ Development Tools

### CSV Import Template
Download the CSV template to understand the required format for bulk imports:
```bash
curl -X GET "https://your-api-domain.com/api/admin/import/template" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -o "template.csv"
```

### API Health Check
Check if the API is running:
```bash
curl -X GET "https://your-api-domain.com/api/public/health"
```

Response:
```json
{
    "status": "ok",
    "timestamp": "2025-07-20T12:00:00.000000Z",
    "service": "backend-api"
}
```

## 📖 Getting Help

### API Information
Get general API information:
```bash
curl -X GET "https://your-api-domain.com/api/public/info"
```

### Support Resources
- **Documentation:** Individual controller documentation files
- **Examples:** Curl examples in each endpoint documentation
- **CSV Format:** Detailed CSV format guide in ImportController.md
- **Error Codes:** Comprehensive error handling in each controller doc

## 🔗 Related Documentation
- [Course Controller](./CourseController.md) - Complete course management
- [Unit Controller](./UnitController.md) - Learning unit operations  
- [Assessment Controller](./AssessmentMultipleSelectController.md) - Quiz assessments
- [Import Controller](./ImportController.md) - Bulk CSV import system

---

**Last Updated:** 2025-07-20  
**API Version:** 1.0.0  
**Documentation Version:** 1.0.0