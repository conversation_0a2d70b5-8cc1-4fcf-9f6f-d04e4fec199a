# Unit Types Documentation

## Overview
Units in the English Game Backend can be categorized by type to specify what kind of assessments they contain.

## Available Unit Types

### 1. MULTIPLE_SELECT
- **Value**: `multiple_select`
- **Description**: Units containing multiple choice/select assessments
- **Assessment Type**: `AssessmentMultipleSelect`
- **Use Case**: Traditional multiple choice questions with answer options

### 2. AI_GAP_FILL_SENTENCE
- **Value**: `ai_gap_fill_sentence`
- **Description**: Units containing AI-generated gap fill sentence assessments
- **Assessment Type**: `AssessmentAiGapFillSentence`
- **Use Case**: Fill-in-the-blank exercises with automatic gap position detection

## API Usage

### Creating Units with Type

**Multiple Select Unit:**
```json
{
  "course_id": 1,
  "title": "Basic Grammar - Multiple Choice",
  "description": "Practice basic grammar with multiple choice questions",
  "skill_type": "grammar",
  "difficulty": "beginner",
  "unit_type": "multiple_select"
}
```

**AI Gap Fill Unit:**
```json
{
  "course_id": 1,
  "title": "Basic Grammar - Gap Fill",
  "description": "Practice basic grammar with gap fill exercises",
  "skill_type": "grammar",
  "difficulty": "beginner",
  "unit_type": "ai_gap_fill_sentence"
}
```

### Filtering Units by Type

```bash
# Get only AI Gap Fill units
GET /api/admin/units?unit_type=ai_gap_fill_sentence

# Get only Multiple Select units
GET /api/admin/units?unit_type=multiple_select
```

## Model Methods

### Unit Model Helpers

```php
// Check unit type
$unit->isAiGapFillSentenceUnit(); // boolean
$unit->isMultipleSelectUnit(); // boolean

// Get type-specific assessments
$unit->getTypeSpecificAssessments(); // Collection

// Query scopes
Unit::aiGapFillSentence()->get(); // Only AI gap fill units
Unit::multipleSelect()->get(); // Only multiple select units
Unit::ofType(UnitType::AI_GAP_FILL_SENTENCE)->get(); // Filter by type
```

### UnitType Enum Helpers

```php
// Get description
UnitType::getDescription('ai_gap_fill_sentence'); // "AI Gap Fill Sentence"

// Get all types with descriptions
UnitType::getTypesWithDescriptions(); // Array

// Get corresponding assessment model class
UnitType::getAssessmentModelClass('ai_gap_fill_sentence'); // AssessmentAiGapFillSentence::class

// Get unit type from assessment model
UnitType::fromAssessmentModelClass(AssessmentAiGapFillSentence::class); // 'ai_gap_fill_sentence'
```

## Database Schema

### Migration
The `unit_type` field was added to the `units` table:
- **Type**: `string`
- **Default**: `multiple_select`
- **Values**: `ai_gap_fill_sentence`, `multiple_select`

### Validation Rules
- **Create**: `required|enum:ai_gap_fill_sentence,multiple_select`
- **Update**: `sometimes|required|enum:ai_gap_fill_sentence,multiple_select`

## Seeder Behavior

### UnitSeeder
- Creates general-purpose units with `unit_type = 'multiple_select'`
- Suitable for traditional multiple choice assessments

### AssessmentAiGapFillSentenceSeeder
- Creates specialized units with `unit_type = 'ai_gap_fill_sentence'`
- Optimized for gap fill exercises
- Automatically assigns appropriate assessments to matching unit types