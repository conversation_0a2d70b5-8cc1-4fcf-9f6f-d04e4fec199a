# AdminAuth Middleware Implementation Alternatives

## Current Implementation (Using Specific Guard)

```php
// Get authenticated staff user using the staff guard
$staff = $request->user('staff');

if (!$staff) {
    return response()->json([
        'success' => false,
        'message' => 'Invalid or expired admin token'
    ], 401);
}

// Verify the authenticated user is actually a Staff instance
if (!($staff instanceof Staff)) {
    return response()->json([
        'success' => false,
        'message' => 'Admin access required'
    ], 403);
}
```

## Alternative: Using Default Guard with Route Middleware

If you want to use `$request->user()` without specifying the guard, you can:

### Option 1: Apply auth:staff middleware to routes
```php
// In routes/api.php
Route::prefix('admin')->middleware(['auth:staff', 'admin.auth'])->group(function () {
    // Your routes here
});

// In AdminAuth middleware
$user = $request->user(); // This will now return Staff instance
if (!$user) {
    return response()->json([
        'success' => false,
        'message' => 'Invalid or expired admin token'
    ], 401);
}

// Verify it's a Staff instance (optional since auth:staff already ensures this)
if (!($user instanceof Staff)) {
    return response()->json([
        'success' => false,
        'message' => 'Admin access required'
    ], 403);
}

$staff = $user;
```

### Option 2: Set default guard for API routes
```php
// In config/auth.php
'defaults' => [
    'guard' => 'staff', // Change from 'web' to 'staff'
    'passwords' => 'users',
],

// In AdminAuth middleware
$user = $request->user(); // This will use the staff guard by default
if (!$user) {
    return response()->json([
        'success' => false,
        'message' => 'Invalid or expired admin token'
    ], 401);
}

if (!($user instanceof Staff)) {
    return response()->json([
        'success' => false,
        'message' => 'Admin access required'
    ], 403);
}

$staff = $user;
```

### Option 3: Custom Middleware Order
```php
// Create a custom middleware that sets the guard context
class SetStaffGuard
{
    public function handle($request, Closure $next)
    {
        // Set the default guard for this request
        config(['auth.defaults.guard' => 'staff']);
        return $next($request);
    }
}

// Apply it before AdminAuth
Route::prefix('admin')->middleware(['set.staff.guard', 'admin.auth'])->group(function () {
    // Your routes
});

// In AdminAuth middleware
$user = $request->user(); // Will use staff guard due to config change
```

## Pros and Cons

### Current Approach: `$request->user('staff')`
**Pros:**
- Explicit and clear which guard is being used
- No dependency on route middleware order
- Works regardless of default guard configuration
- Self-contained middleware

**Cons:**
- Slightly more verbose
- Need to remember to specify the guard

### Alternative: `$request->user()`
**Pros:**
- Cleaner, shorter syntax
- More "Laravel-like" if you're used to default guards

**Cons:**
- Requires additional middleware or configuration
- Less explicit about which guard is being used
- Can be confusing if multiple guards are in use
- Dependency on route configuration or global settings

## Final Implementation

We implemented a simple two-middleware approach:

### 1. AdminAuth Middleware (for Staff Authentication)
```php
// Uses $request->user('staff') - explicit and reliable
$staff = $request->user('staff');
```

### 2. CheckUserBlocked Middleware (for Authenticated Users)
```php
// Uses $request->user() - assumes auth middleware ran first
$user = $request->user();
```

## Why This Approach?

1. **AdminAuth** uses `$request->user('staff')` because:
   - Sanctum tokens are model-specific
   - Staff tokens work only with the 'staff' guard
   - Explicit and reliable

2. **CheckUserBlocked** uses `$request->user()` because:
   - Applied after authentication middleware (auth:sanctum)
   - Already knows the user is authenticated
   - Cleaner and simpler

3. **Public endpoints** (like `/users/sync`):
   - No blocking middleware applied
   - Users can register/login even if blocked
   - Blocking only affects authenticated operations

## Recommendation

Use the appropriate approach for each scenario:
- **Staff operations**: Use `$request->user('staff')` (explicit guard)
- **Authenticated user operations**: Use `$request->user()` (simpler)
- **Public operations**: No blocking middleware needed
