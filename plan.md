REFACTOR:

Route::apiResource('assessments/ai-gap-fill-sentence', AssessmentAiGapFillSentenceController::class)
    ->names('admin.assessments.ai-gap-fill-sentence');

        Route::apiResource('assessments/multiple-select', AssessmentMultipleSelectController::class)
            ->names('admin.assessments.multiple-select');

Don't use resource


 Route::prefix('assessments/ai-gap-fill-sentence/{assessmentAiGapFillSentence}')->group(function () {
            Route::post('/attach-units', [AssessmentAiGapFillSentenceController::class, 'attachToUnits'])
                ->name('admin.assessments.ai-gap-fill-sentence.attach-units');
            Route::post('/detach-units', [AssessmentAiGapFillSentenceController::class, 'detachFromUnits'])
                ->name('admin.assessments.ai-gap-fill-sentence.detach-units');
        });
        Route::prefix('assessments/multiple-select/{assessmentMultipleSelect}')->group(function () {
            Route::post('/attach-units', [AssessmentMultipleSelectController::class, 'attachToUnits'])
                ->name('admin.assessments.multiple-select.attach-units');
            Route::post('/detach-units', [AssessmentMultipleSelectController::class, 'detachFromUnits'])
                ->name('admin.assessments.multiple-select.detach-units');
        });

Should use same controller (AssessmentController)
Only api about create, update, delete

when create, update => Base on type unit => implement Strategy about that.
